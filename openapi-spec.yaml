openapi: 3.0.0
paths:
  /:
    get:
      operationId: App<PERSON><PERSON><PERSON>er_getHello
      parameters: []
      responses:
        '200':
          description: ''
      tags:
        - App
  /user:
    post:
      operationId: User<PERSON>ontroller_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserDto'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  email:
                    type: string
                  role:
                    type: string
                    enum:
                      - teacher
                      - admin
        '400':
          description: Bad request
        '403':
          description: Forbidden - Independent teachers cannot create user accounts
      security: &ref_0
        - bearer: []
      summary: Create a new user
      tags: &ref_1
        - User
    get:
      operationId: UserController_findAll
      parameters:
        - name: schoolId
          required: false
          in: query
          description: Filter users by school ID (UUID format)
          schema:
            example: 123e4567-e89b-12d3-a456-************
            type: string
        - name: role
          required: false
          in: query
          description: Filter users by role
          schema:
            $ref: '#/components/schemas/EUserRole'
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                    name:
                      type: string
                    email:
                      type: string
                    role:
                      type: string
                      enum:
                        - teacher
                        - admin
                        - student
                        - school_manager
                    schoolId:
                      type: string
                      nullable: true
      security: *ref_0
      summary: Get all users with optional filtering
      tags: *ref_1
  /user/me:
    get:
      operationId: UserController_me
      parameters: []
      responses:
        '200':
          description: Current user profile retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  email:
                    type: string
                  role:
                    type: string
                    enum:
                      - teacher
                      - admin
        '401':
          description: Unauthorized
      security: *ref_0
      summary: Get current user profile
      tags: *ref_1
  /user/{id}:
    get:
      operationId: UserController_findOne
      parameters:
        - name: id
          required: true
          in: path
          description: User ID
          schema:
            type: string
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  email:
                    type: string
                  role:
                    type: string
                    enum:
                      - teacher
                      - admin
        '404':
          description: User not found
      security: *ref_0
      summary: Get user by ID
      tags: *ref_1
    patch:
      operationId: UserController_update
      parameters:
        - name: id
          required: true
          in: path
          description: User ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserDto'
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  email:
                    type: string
                  role:
                    type: string
                    enum:
                      - teacher
                      - admin
                      - student
                      - school_manager
        '400':
          description: Bad request
        '404':
          description: User not found
      security: *ref_0
      summary: Update a user
      tags: *ref_1
  /auth/sign-in:
    post:
      operationId: AuthController_signIn
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignInDto'
      responses:
        '200':
          description: User signed in successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  accessToken:
                    type: string
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                      name:
                        type: string
                      email:
                        type: string
        '401':
          description: Unauthorized
      summary: Sign in with email and password
      tags: &ref_2
        - Auth
  /auth/sign-up:
    post:
      operationId: AuthController_signUp
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignUpDto'
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  email:
                    type: string
        '400':
          description: Bad request
      summary: Register a new user
      tags: *ref_2
  /schools:
    post:
      operationId: SchoolController_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSchoolDto'
      responses:
        '201':
          description: The school has been successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/School'
        '403':
          description: Forbidden.
      security: &ref_3
        - bearer: []
      summary: Create a new school
      tags: &ref_4
        - Schools
    get:
      operationId: SchoolController_findAll
      parameters: []
      responses:
        '200':
          description: Return all schools.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/School'
      security: *ref_3
      summary: Get all schools
      tags: *ref_4
  /schools/my-school:
    patch:
      operationId: SchoolController_updateOwnSchool
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSchoolDto'
      responses:
        '200':
          description: The school has been successfully updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/School'
        '403':
          description: Forbidden.
        '404':
          description: School not found or you are not the administrator.
      security: *ref_3
      summary: Update own school (for Independent Teachers)
      tags: *ref_4
  /schools/{id}:
    get:
      operationId: SchoolController_findOne
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Return the school.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/School'
        '404':
          description: School not found.
      security: *ref_3
      summary: Get a school by id
      tags: *ref_4
    patch:
      operationId: SchoolController_update
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSchoolDto'
      responses:
        '200':
          description: The school has been successfully updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/School'
        '403':
          description: Forbidden.
        '404':
          description: School not found.
      security: *ref_3
      summary: Update a school
      tags: *ref_4
    delete:
      operationId: SchoolController_remove
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: The school has been successfully deleted.
        '403':
          description: Forbidden.
        '404':
          description: School not found.
      security: *ref_3
      summary: Delete a school
      tags: *ref_4
  /schools/examination-format:
    post:
      operationId: SchoolController_uploadExaminationFormat
      parameters: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: PDF file containing the examination format
                schoolId:
                  type: string
                  description: School ID for which the examination format applies
      responses:
        '201':
          description: The examination format has been successfully uploaded.
        '400':
          description: Bad request.
        '403':
          description: Forbidden.
      security: *ref_3
      summary: Upload a PDF file defining the examination format for a specific school
      tags: *ref_4
  /schools/{schoolId}/examination-format:
    get:
      operationId: SchoolController_getExaminationFormat
      parameters:
        - name: schoolId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Return the examination format text.
          content:
            application/json:
              schema:
                type: string
        '403':
          description: Forbidden.
        '404':
          description: School or examination format not found.
      security: *ref_3
      summary: Get the examination format for a school
      tags: *ref_4
    delete:
      operationId: SchoolController_deleteExaminationFormat
      parameters:
        - name: schoolId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: The examination format has been successfully deleted.
        '403':
          description: Forbidden.
        '404':
          description: School or examination format not found.
      security: *ref_3
      summary: Delete the examination format for a school
      tags: *ref_4
  /schools/{schoolId}/narrative-structure/extract:
    post:
      operationId: SchoolController_extractNarrativeStructure
      parameters:
        - name: schoolId
          required: true
          in: path
          schema:
            type: string
      responses:
        '201':
          description: Narrative structure extracted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExtractNarrativeStructureResponseDto'
        '400':
          description: Bad request - No examination formats found or extraction failed
        '403':
          description: Forbidden - Admin access required
        '404':
          description: School not found
      security: *ref_3
      summary: >-
        Extract narrative structure from examination formats for a specific
        school
      tags: *ref_4
  /schools/narrative-structure/extract-all:
    post:
      operationId: SchoolController_extractAllNarrativeStructures
      parameters: []
      responses:
        '201':
          description: Bulk narrative structure extraction completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkExtractNarrativeStructureResponseDto'
        '403':
          description: Forbidden - Admin access required
      security: *ref_3
      summary: Extract narrative structures for all schools with examination formats
      tags: *ref_4
  /schools/{schoolId}/narrative-structure:
    get:
      operationId: SchoolController_getNarrativeStructure
      parameters:
        - name: schoolId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Return the narrative structure
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NarrativeStructureResponseDto'
        '403':
          description: Forbidden - Admin access required
        '404':
          description: School or narrative structure not found
      security: *ref_3
      summary: Get the narrative structure for a school
      tags: *ref_4
    delete:
      operationId: SchoolController_deleteNarrativeStructure
      parameters:
        - name: schoolId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Narrative structure deleted successfully
        '403':
          description: Forbidden - Admin access required
        '404':
          description: School or narrative structure not found
      security: *ref_3
      summary: Delete the narrative structure for a school
      tags: *ref_4
  /brands:
    post:
      operationId: BrandController_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBrandDto'
      responses:
        '201':
          description: The brand has been successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Brand'
        '403':
          description: Forbidden.
      security: &ref_5
        - bearer: []
      summary: Create a new brand
      tags: &ref_6
        - Brands
    get:
      operationId: BrandController_findAll
      parameters: []
      responses:
        '200':
          description: Return all brands.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Brand'
      security: *ref_5
      summary: Get all brands
      tags: *ref_6
  /brands/{id}:
    get:
      operationId: BrandController_findOne
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Return the brand.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Brand'
        '404':
          description: Brand not found.
      security: *ref_5
      summary: Get a brand by id
      tags: *ref_6
    patch:
      operationId: BrandController_update
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateBrandDto'
      responses:
        '200':
          description: The brand has been successfully updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Brand'
        '403':
          description: Forbidden.
        '404':
          description: Brand not found.
      security: *ref_5
      summary: Update a brand
      tags: *ref_6
    delete:
      operationId: BrandController_remove
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: The brand has been successfully deleted.
        '403':
          description: Forbidden.
        '404':
          description: Brand not found.
      security: *ref_5
      summary: Delete a brand
      tags: *ref_6
  /documents:
    get:
      operationId: DocumentsController_queryDocuments
      parameters:
        - name: query
          required: true
          in: query
          description: Search term to query documents
          schema:
            type: string
        - name: category
          required: false
          in: query
          description: Category to filter documents (optional)
          schema:
            type: string
      responses:
        '200':
          description: Documents retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: number
                    example: 200
                  message:
                    type: string
                    example: Documents retrieved successfully
                  data:
                    type: string
                    example: Document content...
        '400':
          description: Bad request
      summary: Query documents based on search term and optional category
      tags: &ref_7
        - Documents
  /documents/upload:
    post:
      operationId: DocumentsController_uploadDocuments
      parameters: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                title:
                  type: string
                  description: Title of the document(s)
                description:
                  type: string
                  description: Description of the document(s)
                category:
                  type: string
                  description: Category of the document(s)
      responses:
        '200':
          description: Documents uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: number
                    example: 200
                  message:
                    type: string
                    example: Documents uploaded successfully
                  data:
                    type: object
                    properties:
                      count:
                        type: number
                        example: 2
                      results:
                        type: array
                        items:
                          type: object
                          properties:
                            documentId:
                              type: string
                            title:
                              type: string
                            description:
                              type: string
                            fileName:
                              type: string
                            mimeType:
                              type: string
                            uploadedAt:
                              type: string
                              format: date-time
                            category:
                              type: string
        '400':
          description: Bad request
      summary: Upload documents for processing
      tags: *ref_7
  /prompt/exercise:
    post:
      operationId: PromptController_genExercise
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenPromptDto'
      responses:
        '201':
          description: Exercise generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        question:
                          type: string
                        options:
                          type: array
                          items:
                            type: string
                        answer:
                          type: string
                        explanation:
                          type: string
                        image:
                          type: string
        '400':
          description: Bad request
      summary: Generate exercise based on provided content and requirements
      tags:
        - Prompt
  /ai-service-logs:
    get:
      operationId: AiServiceLogController_getAllLogs
      parameters:
        - name: status
          required: false
          in: query
          schema:
            enum:
              - success
              - error
            type: string
        - name: worksheetId
          required: false
          in: query
          schema:
            type: string
        - name: startDate
          required: false
          in: query
          schema:
            format: date-time
            type: string
        - name: endDate
          required: false
          in: query
          schema:
            format: date-time
            type: string
      responses:
        '200':
          description: Returns all AI service logs matching the filter criteria
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AiServiceLog'
      summary: Get all AI service logs with optional filtering
      tags: &ref_8
        - AI Service Logs
  /ai-service-logs/worksheet/{worksheetId}:
    get:
      operationId: AiServiceLogController_getLogsByWorksheetId
      parameters:
        - name: worksheetId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Returns AI service logs for the specified worksheet
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AiServiceLog'
      summary: Get AI service logs by worksheet ID
      tags: *ref_8
  /ai-service-logs/batch/{batchId}:
    get:
      operationId: AiServiceLogController_getLogByBatchId
      parameters:
        - name: batchId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: >-
            Returns the AI service log for the specified batch or null if not
            found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AiServiceLog'
      summary: Get AI service log by batch ID
      tags: *ref_8
  /files/upload:
    post:
      operationId: FilesController_uploadFile
      parameters: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                description:
                  type: string
                category:
                  type: string
                tags:
                  type: array
                  items:
                    type: string
      responses:
        '201':
          description: ''
      summary: Upload a single file
      tags: &ref_9
        - files
  /files/upload/multiple:
    post:
      operationId: FilesController_uploadFiles
      parameters: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                description:
                  type: string
                category:
                  type: string
                tags:
                  type: array
                  items:
                    type: string
      responses:
        '201':
          description: ''
      summary: Upload multiple files
      tags: *ref_9
  /files:
    get:
      operationId: FilesController_getFiles
      parameters:
        - name: page
          required: false
          in: query
          description: Page number
          schema:
            default: 1
            type: number
        - name: limit
          required: false
          in: query
          description: Number of items per page
          schema:
            default: 10
            type: number
        - name: category
          required: false
          in: query
          description: Category of the file
          schema:
            type: string
        - name: tags
          required: false
          in: query
          description: Tags for the file
          schema:
            type: array
            items:
              type: string
        - name: uploadedBy
          required: false
          in: query
          description: User ID who uploaded the file
          schema:
            type: string
        - name: sortBy
          required: false
          in: query
          description: Field to sort by
          schema:
            default: createdAt
            type: string
        - name: sortOrder
          required: false
          in: query
          description: Sort order
          schema:
            default: desc
            type: string
            enum:
              - asc
              - desc
      responses:
        '200':
          description: ''
      summary: Get files with pagination and filtering
      tags: *ref_9
  /files/{fileId}:
    get:
      operationId: FilesController_getFileById
      parameters:
        - name: fileId
          required: true
          in: path
          description: File ID
          schema:
            type: string
      responses:
        '200':
          description: ''
      summary: Get file metadata by ID
      tags: *ref_9
    put:
      operationId: FilesController_updateFile
      parameters:
        - name: fileId
          required: true
          in: path
          description: File ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FileUpdateDto'
      responses:
        '200':
          description: ''
      summary: Update file metadata
      tags: *ref_9
    delete:
      operationId: FilesController_deleteFile
      parameters:
        - name: fileId
          required: true
          in: path
          description: File ID
          schema:
            type: string
      responses:
        '200':
          description: ''
      summary: Delete file
      tags: *ref_9
  /files/{fileId}/download:
    get:
      operationId: FilesController_downloadFile
      parameters:
        - name: fileId
          required: true
          in: path
          description: File ID
          schema:
            type: string
      responses:
        '200':
          description: ''
      summary: Download file by ID
      tags: *ref_9
  /files/render/{id}:
    get:
      operationId: FilesController_renderFile
      parameters:
        - name: id
          required: true
          in: path
          description: File ID
          schema:
            type: string
      responses:
        '200':
          description: File rendered successfully.
        '400':
          description: Failed to render file.
        '404':
          description: File not found.
      summary: Render file by ID in browser
      tags: *ref_9
  /files/images/{filename}:
    get:
      operationId: ImagesController_getImage
      parameters:
        - name: filename
          required: true
          in: path
          description: Image filename
          schema:
            type: string
      responses:
        '200':
          description: ''
      summary: Get an image file by filename
      tags:
        - images
  /question-pool/metrics:
    get:
      operationId: QuestionPoolMetricsController_getMetrics
      parameters: []
      responses:
        '200':
          description: Prometheus metrics in text format
          content:
            text/plain:
              schema:
                type: string
                example: >-
                  # HELP question_pool_query_duration_seconds Duration of
                  question pool queries in seconds

                  # TYPE question_pool_query_duration_seconds histogram

                  question_pool_query_duration_seconds_bucket{le="0.001",method="getRandomQuestions",cache_status="miss"}
                  0

                  ...
      summary: Get Prometheus metrics for question pool operations
      tags: &ref_10
        - Question Pool Metrics
  /question-pool/metrics/summary:
    get:
      operationId: QuestionPoolMetricsController_getPerformanceSummary
      parameters: []
      responses:
        '200':
          description: Performance summary with key metrics
          content:
            application/json:
              schema:
                type: object
                properties:
                  totalQueries:
                    type: number
                    description: Total number of queries executed
                  cacheHitRate:
                    type: number
                    description: Cache hit rate (0-1)
                  averageQueryTime:
                    type: number
                    description: Average query time in seconds
                  errorRate:
                    type: number
                    description: Error rate (0-1)
      summary: Get performance summary for question pool operations
      tags: *ref_10
  /question-pool/metrics/cache-stats:
    get:
      operationId: QuestionPoolMetricsController_getCacheStats
      parameters: []
      responses:
        '200':
          description: Cache statistics
          content:
            application/json:
              schema:
                type: object
                properties:
                  hitRate:
                    type: number
                    description: Cache hit rate (0-1)
                  totalHits:
                    type: number
                    description: Total cache hits
                  totalMisses:
                    type: number
                    description: Total cache misses
      summary: Get cache statistics for question pool
      tags: *ref_10
  /question-pool/search:
    get:
      description: >-
        Search and filter questions from the question pool with comprehensive
        filtering options.
            
            **Access Control:**
            - **Admin users**: Can search all questions in the pool
            - **School Manager**: Can search questions relevant to their school context
            - **Teacher**: Can search questions relevant to their school context
            - **Independent Teacher**: Can search all public questions
            
            **Features:**
            - Full-text search in question content
            - Multiple filter options (subject, grade, type, difficulty, etc.)
            - Pagination support
            - Sorting options
            - Exclude recently used questions
            
      operationId: QuestionPoolController_searchQuestions
      parameters:
        - name: subject
          required: false
          in: query
          description: Subject filter
          schema:
            example: Mathematics
            type: string
        - name: parentSubject
          required: false
          in: query
          description: Parent subject filter
          schema:
            example: Mathematics
            type: string
        - name: childSubject
          required: false
          in: query
          description: Child subject filter
          schema:
            example: Algebra
            type: string
        - name: type
          required: false
          in: query
          description: Question type filter
          schema:
            example:
              - multiple_choice
              - true_false
            type: array
            items:
              type: string
              enum:
                - multiple_choice
                - true_false
                - short_answer
                - long_answer
                - fill_in_the_blank
                - matching
                - ordering
                - calculation
                - diagram
                - essay
        - name: grade
          required: false
          in: query
          description: Grade level filter
          schema:
            example: Primary 2
            type: string
        - name: difficulty
          required: false
          in: query
          description: Question difficulty filter
          schema:
            type: array
            items:
              type: string
              enum:
                - very_easy
                - easy
                - medium
                - hard
                - very_hard
        - name: status
          required: false
          in: query
          description: Question status filter
          schema:
            default: active
            type: string
            enum:
              - active
              - inactive
              - draft
              - archived
              - under_review
        - name: language
          required: false
          in: query
          description: Language filter
          schema:
            default: English
            example: English
            type: string
        - name: searchText
          required: false
          in: query
          description: Search text in question content
          schema:
            example: capital of France
            type: string
        - name: tags
          required: false
          in: query
          description: Tags filter
          schema:
            example:
              - geometry
              - basic
            type: array
            items:
              type: string
        - name: optionTypeId
          required: false
          in: query
          description: Option type ID filter
          schema:
            example: 123e4567-e89b-12d3-a456-************
            type: string
        - name: optionValueId
          required: false
          in: query
          description: Option value ID filter
          schema:
            example: 987fcdeb-51a2-43d1-b789-123456789abc
            type: string
        - name: page
          required: false
          in: query
          description: Page number for pagination
          schema:
            minimum: 1
            default: 1
            type: number
        - name: limit
          required: false
          in: query
          description: Number of items per page
          schema:
            minimum: 1
            maximum: 100
            default: 20
            type: number
        - name: sortBy
          required: false
          in: query
          description: Sort field
          schema:
            default: createdAt
            type: string
            enum:
              - createdAt
              - updatedAt
              - subject
              - grade
              - type
        - name: sortOrder
          required: false
          in: query
          description: Sort order
          schema:
            default: desc
            type: string
            enum:
              - asc
              - desc
        - name: hasMedia
          required: false
          in: query
          description: Include questions with media only
          schema:
            default: false
            type: boolean
        - name: excludeRecentlyUsedHours
          required: false
          in: query
          description: Exclude questions that have been used recently (in hours)
          schema:
            minimum: 0
            maximum: 168
            type: number
        - name: excludeQuestionIds
          required: false
          in: query
          description: Exclude specific question IDs that user has already chosen
          schema:
            example:
              - 507f1f77bcf86cd799439011
              - 507f1f77bcf86cd799439012
            type: array
            items:
              type: string
        - name: excludeFromWorksheets
          required: false
          in: query
          description: >-
            Exclude questions from specific worksheet IDs that user has access
            to
          schema:
            example:
              - worksheet-uuid-1
              - worksheet-uuid-2
            type: array
            items:
              type: string
        - name: excludeUserChosenQuestions
          required: false
          in: query
          description: Automatically exclude questions already used by the current user
          schema:
            default: true
            type: boolean
      responses:
        '200':
          description: Questions retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionPoolSearchResultDto'
        '400':
          description: Bad request - Invalid search parameters
        '403':
          description: Forbidden - Insufficient permissions
      security: &ref_11
        - bearer: []
      summary: Search questions in the question pool
      tags: &ref_12
        - Question Pool
  /question-pool/filters:
    get:
      description: >-
        Returns all available filter options including subjects, grades, types,
        difficulties, and languages available in the question pool.
      operationId: QuestionPoolController_getFilterOptions
      parameters: []
      responses:
        '200':
          description: Filter options retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionPoolFiltersDto'
      security: *ref_11
      summary: Get available filter options for question pool search
      tags: *ref_12
  /question-pool/{id}:
    get:
      description: >-
        Retrieve detailed information about a specific question from the
        question pool.
      operationId: QuestionPoolController_getQuestionById
      parameters:
        - name: id
          required: true
          in: path
          description: Question ID
          schema:
            type: string
      responses:
        '200':
          description: Question retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionPoolResponseDto'
        '403':
          description: Forbidden - Insufficient permissions
        '404':
          description: Question not found
      security: *ref_11
      summary: Get a specific question from the pool by ID
      tags: *ref_12
  /question-pool/user/stats:
    get:
      description: >-
        Returns statistics about the current user's question usage including
        total questions used, worksheets created, and most used subjects.
      operationId: QuestionPoolController_getUserQuestionStats
      parameters: []
      responses:
        '200':
          description: User statistics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  totalWorksheetsCreated:
                    type: number
                  totalQuestionsUsed:
                    type: number
                  uniquePoolQuestionsUsed:
                    type: number
                  customQuestionsCreated:
                    type: number
                  mostUsedSubjects:
                    type: array
                    items:
                      type: object
                      properties:
                        subject:
                          type: string
                        count:
                          type: number
      security: *ref_11
      summary: Get user question usage statistics
      tags: *ref_12
  /worksheets:
    post:
      operationId: WorksheetController_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWorksheetDto'
      responses:
        '201':
          description: Worksheet created successfully
        '400':
          description: Bad request
      security: &ref_13
        - bearer: []
      summary: Create a new worksheet
      tags: &ref_14
        - Worksheet
    get:
      description: |-
        Retrieves worksheets with pagination support and school-based filtering.

            **School-Based Filtering Behavior:**
            - **Admin users**: Can optionally specify a schoolId to filter worksheets from a specific school. If no schoolId is provided, all worksheets are returned.
            - **Non-admin users** (SCHOOL_MANAGER, TEACHER, INDEPENDENT_TEACHER): Can only access worksheets from their own school. If a schoolId is provided, it must match their own school ID, otherwise a 403 Forbidden error is returned.
            - **Users without school association**: Cannot access any worksheets and will receive empty results.

            **Parameters:**
            - page: Page number for pagination (starts at 1)
            - pageSize: Number of items per page
            - schoolId: Optional UUID to filter worksheets by school (admin users only for cross-school access)
      operationId: WorksheetController_findAll
      parameters:
        - name: page
          required: false
          in: query
          description: Page number for pagination (starts at 1)
          schema:
            default: 1
            example: 1
            type: number
        - name: pageSize
          required: false
          in: query
          description: Number of items per page
          schema:
            default: 10
            example: 10
            type: number
        - name: schoolId
          required: false
          in: query
          description: >-
            Filter worksheets by school ID. Admin users can specify any school
            ID to filter worksheets. Non-admin users can only access worksheets
            from their own school. If not provided, non-admin users will see
            only their school's worksheets, while admin users will see all
            worksheets.
          schema:
            example: 123e4567-e89b-12d3-a456-************
            type: string
      responses:
        '200':
          description: Worksheets retrieved successfully
          content:
            application/json:
              schema:
                properties:
                  items:
                    type: array
                    description: Array of worksheet items for the current page
                    items:
                      type: object
                  meta:
                    type: object
                    description: Pagination metadata
                    properties:
                      page:
                        type: number
                        description: Current page number
                        example: 1
                      pageSize:
                        type: number
                        description: Number of items per page
                        example: 10
                      total:
                        type: number
                        description: Total number of items across all pages
                        example: 100
                      totalPages:
                        type: number
                        description: Total number of pages
                        example: 10
        '400':
          description: >-
            Bad Request - Invalid query parameters (e.g., invalid UUID format
            for schoolId)
        '401':
          description: Unauthorized - Missing or invalid authentication token
        '403':
          description: >-
            Forbidden - User attempted to access worksheets from a different
            school or user is not associated with any school
      security: *ref_13
      summary: Get all worksheets with pagination and school-based filtering
      tags: *ref_14
  /worksheets/cache/metrics:
    get:
      operationId: WorksheetController_getCacheMetrics
      parameters: []
      responses:
        '200':
          description: Cache metrics retrieved successfully
      security: *ref_13
      summary: Get document cache metrics
      tags: *ref_14
  /worksheets/cache/warm:
    get:
      operationId: WorksheetController_triggerCacheWarming
      parameters: []
      responses:
        '200':
          description: Cache warming triggered successfully
      security: *ref_13
      summary: Manually trigger cache warming
      tags: *ref_14
    post:
      description: >-
        Triggers cache warming process for popular worksheets and frequently
        accessed data
      operationId: WorksheetCacheManagementController_warmCache
      parameters: []
      responses:
        '200':
          description: Cache warming initiated successfully
        '403':
          description: Forbidden - Admin access required
      security: &ref_17
        - bearer: []
      summary: Manually trigger cache warming
      tags: &ref_18
        - Worksheet Cache Management
  /worksheets/{id}/questions:
    post:
      description: >-
        Add a new question to an existing worksheet with comprehensive
        validation and access control.

            **Access Control:**
            - **Admin users**: Can add questions to any worksheet
            - **School Manager**: Can add questions to worksheets in their school only
            - **Teacher**: Can add questions to worksheets in their school only
            - **Independent Teacher**: Can add questions to their own worksheets only

            **Validation:**
            - Validates question data structure and required fields
            - Enforces question limit (max 100 questions per worksheet)
            - Ensures school-based data isolation

            **Features:**
            - Real-time updates via WebSocket
            - Audit logging for compliance
            - MongoDB cache synchronization
            - Comprehensive error handling
      operationId: WorksheetController_addQuestion
      parameters:
        - name: id
          required: true
          in: path
          description: Worksheet ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddQuestionToWorksheetDto'
      responses:
        '201':
          description: Question added successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    description: The created question object
                  message:
                    type: string
                    example: Question added successfully
        '400':
          description: Bad request - Invalid question data or limit exceeded
        '403':
          description: Forbidden - Insufficient permissions or school access denied
        '404':
          description: Worksheet not found
        '409':
          description: Conflict - Concurrent modification detected
      security: *ref_13
      summary: Add a question to a worksheet
      tags: *ref_14
  /worksheets/{id}/questions/{questionId}:
    delete:
      description: >-
        Remove an existing question from a worksheet with comprehensive
        validation and access control.

            **Access Control:**
            - **Admin users**: Can remove questions from any worksheet
            - **School Manager**: Can remove questions from worksheets in their school only
            - **Teacher**: Can remove questions from worksheets in their school only
            - **Independent Teacher**: Can remove questions from their own worksheets only

            **Validation:**
            - Validates that the question exists in the specified worksheet
            - Prevents removal if it would result in zero questions (minimum 1 required)
            - Ensures school-based data isolation
            - Validates user permissions before allowing removal

            **Side Effects:**
            - Reorders remaining questions to maintain sequential order
            - Updates MongoDB cache for the worksheet
            - Emits real-time WebSocket event to notify collaborators
            - Creates audit log entry for the removal
            - Updates worksheet metadata (totalQuestions, lastModifiedBy)

            **Real-time Updates:**
            - Broadcasts 'question_removed' event to all users viewing the worksheet
            - Includes updated question count and worksheet metadata
      operationId: WorksheetController_removeQuestion
      parameters:
        - name: id
          required: true
          in: path
          description: Worksheet ID
          schema:
            type: string
        - name: questionId
          required: true
          in: path
          description: Question ID to remove
          schema:
            type: string
      responses:
        '204':
          description: Question removed successfully
        '400':
          description: Bad request - Cannot remove last question or invalid parameters
        '403':
          description: Forbidden - Insufficient permissions or school access denied
        '404':
          description: Worksheet or question not found
        '409':
          description: Conflict - Concurrent modification detected
      security: *ref_13
      summary: Remove a question from a worksheet
      tags: *ref_14
    patch:
      description: >-
        Partially update an existing question in a worksheet with comprehensive
        validation and access control.

            **Access Control:**
            - **Admin users**: Can update questions in any worksheet
            - **School Manager**: Can update questions in worksheets in their school only
            - **Teacher**: Can update questions in worksheets in their school only
            - **Independent Teacher**: Can update questions in their own worksheets only

            **Features:**
            - Partial updates - only provided fields are updated
            - Optimistic locking using version numbers to prevent concurrent conflicts
            - Comprehensive validation including educational standards compliance
            - Real-time WebSocket notifications to collaborators
            - Detailed audit logging with change tracking
            - MongoDB cache synchronization
            - Version history tracking

            **Validation:**
            - Validates question data structure and educational standards
            - Checks answer format based on question type
            - Validates image prompt requirements (dimensions, measurements)
            - Ensures school-based data isolation
            - Prevents concurrent modification conflicts
      operationId: WorksheetController_updateQuestion
      parameters:
        - name: id
          required: true
          in: path
          description: Worksheet ID
          schema:
            type: string
        - name: questionId
          required: true
          in: path
          description: Question ID to update
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWorksheetQuestionDto'
      responses:
        '200':
          description: Question updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    description: The updated question object
                  message:
                    type: string
                    example: Question updated successfully
        '400':
          description: Bad request - Invalid question data or validation errors
        '403':
          description: Forbidden - Insufficient permissions or school access denied
        '404':
          description: Worksheet or question not found
        '409':
          description: Conflict - Concurrent modification detected (version mismatch)
      security: *ref_13
      summary: Update a question in a worksheet (partial update)
      tags: *ref_14
    put:
      description: >-
        Completely replace an existing question in a worksheet with
        comprehensive validation and access control.

            **Access Control:**
            - **Admin users**: Can replace questions in any worksheet
            - **School Manager**: Can replace questions in worksheets in their school only
            - **Teacher**: Can replace questions in worksheets in their school only
            - **Independent Teacher**: Can replace questions in their own worksheets only

            **Features:**
            - Full replacement - all question data is replaced with provided data
            - Optimistic locking using version numbers to prevent concurrent conflicts
            - Comprehensive validation including educational standards compliance
            - Real-time WebSocket notifications to collaborators
            - Detailed audit logging with complete change tracking
            - MongoDB cache synchronization
            - Version history tracking

            **Validation:**
            - Validates complete question data structure and educational standards
            - Checks answer format based on question type
            - Validates image prompt requirements (dimensions, measurements)
            - Ensures school-based data isolation
            - Prevents concurrent modification conflicts
            - Requires version number for optimistic locking
      operationId: WorksheetController_replaceQuestion
      parameters:
        - name: id
          required: true
          in: path
          description: Worksheet ID
          schema:
            type: string
        - name: questionId
          required: true
          in: path
          description: Question ID to replace
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReplaceWorksheetQuestionDto'
      responses:
        '200':
          description: Question replaced successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    description: The replaced question object
                  message:
                    type: string
                    example: Question replaced successfully
        '400':
          description: Bad request - Invalid question data or validation errors
        '403':
          description: Forbidden - Insufficient permissions or school access denied
        '404':
          description: Worksheet or question not found
        '409':
          description: Conflict - Concurrent modification detected (version mismatch)
      security: *ref_13
      summary: Replace a question in a worksheet (full replacement)
      tags: *ref_14
  /worksheets/{id}/questions/reorder:
    patch:
      description: >-
        Reorder questions within a worksheet, supporting both single and bulk
        operations with comprehensive validation and access control.

            **Access Control:**
            - **Admin users**: Can reorder questions in any worksheet
            - **School Manager**: Can reorder questions in worksheets in their school only
            - **Teacher**: Can reorder questions in worksheets in their school only
            - **Independent Teacher**: Can reorder questions in their own worksheets only

            **Features:**
            - Supports bulk reordering of multiple questions
            - Validates all question IDs belong to the specified worksheet
            - Ensures new positions are valid within the worksheet's question count
            - Implements optimistic locking to prevent race conditions
            - Updates question order in PostgreSQL database
            - Synchronizes changes with MongoDB cache
            - Emits real-time WebSocket notifications
            - Creates comprehensive audit log entries

            **Validation:**
            - All provided question IDs must exist in the worksheet
            - New positions must be within valid range (1 to total questions)
            - No duplicate positions allowed
            - Optimistic locking prevents concurrent modifications
            - School-based data isolation enforced

            **Side Effects:**
            - Updates question positions in the database
            - Synchronizes MongoDB cache
            - Emits WebSocket events for real-time collaboration
            - Creates audit log entries for all reorder operations
            - Updates worksheet metadata (lastModifiedBy, questionVersion)
            
      operationId: WorksheetController_reorderQuestions
      parameters:
        - name: id
          required: true
          in: path
          description: Worksheet ID
          schema:
            type: string
      requestBody:
        required: true
        description: Array of question reorder operations
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkReorderQuestionsDto'
            examples:
              singleReorder:
                summary: Single question reorder
                value:
                  reorders:
                    - questionId: question-123
                      newPosition: 3
              bulkReorder:
                summary: Multiple questions reorder
                value:
                  reorders:
                    - questionId: question-123
                      newPosition: 1
                    - questionId: question-456
                      newPosition: 2
                    - questionId: question-789
                      newPosition: 3
      responses:
        '200':
          description: Questions reordered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Questions reordered successfully
                  data:
                    type: object
                    properties:
                      worksheetId:
                        type: string
                        example: worksheet-123
                      totalQuestions:
                        type: number
                        example: 10
                      reorderedQuestions:
                        type: array
                        items:
                          type: object
                          properties:
                            questionId:
                              type: string
                              example: question-123
                            oldPosition:
                              type: number
                              example: 5
                            newPosition:
                              type: number
                              example: 3
                      version:
                        type: number
                        example: 15
        '400':
          description: Bad request - Invalid question IDs, positions, or validation errors
        '403':
          description: Forbidden - Insufficient permissions or school access denied
        '404':
          description: Worksheet not found or question IDs not found in worksheet
        '409':
          description: Conflict - Concurrent modification detected (optimistic locking)
      security: *ref_13
      summary: Reorder questions in a worksheet
      tags: *ref_14
  /worksheets/{id}/questions/bulk-add:
    post:
      description: >-
        Add multiple questions to a worksheet in a single operation with
        comprehensive validation and access control.

            **Access Control:**
            - **Admin users**: Can add questions to any worksheet
            - **School Manager**: Can add questions to worksheets in their school only
            - **Teacher**: Can add questions to worksheets in their school only
            - **Independent Teacher**: Can add questions to their own worksheets only

            **Features:**
            - Supports adding up to 50 questions at once
            - Validates each question before adding
            - Implements transaction-like behavior (partial success allowed)
            - Updates worksheet metadata and cache
            - Emits real-time updates via WebSocket
            - Comprehensive audit logging
            - Respects worksheet question limits
            
      operationId: WorksheetController_bulkAddQuestions
      parameters:
        - name: id
          required: true
          in: path
          description: Worksheet ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkAddQuestionsDto'
      responses:
        '201':
          description: Bulk add operation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkOperationResponseDto'
        '400':
          description: Bad request - Invalid input data or question limit exceeded
        '403':
          description: Forbidden - Insufficient permissions or school access denied
        '404':
          description: Worksheet not found
      security: *ref_13
      summary: Bulk add questions to a worksheet
      tags: *ref_14
  /worksheets/{id}/questions/bulk-remove:
    delete:
      description: >-
        Remove multiple questions from a worksheet in a single operation with
        comprehensive validation and access control.

            **Access Control:**
            - **Admin users**: Can remove questions from any worksheet
            - **School Manager**: Can remove questions from worksheets in their school only
            - **Teacher**: Can remove questions from worksheets in their school only
            - **Independent Teacher**: Can remove questions from their own worksheets only

            **Features:**
            - Supports removing up to 50 questions at once
            - Validates minimum question requirements
            - Implements transaction-like behavior (partial success allowed)
            - Reorders remaining questions automatically
            - Updates worksheet metadata and cache
            - Emits real-time updates via WebSocket
            - Comprehensive audit logging
            
      operationId: WorksheetController_bulkRemoveQuestions
      parameters:
        - name: id
          required: true
          in: path
          description: Worksheet ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkRemoveQuestionsDto'
      responses:
        '200':
          description: Bulk remove operation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkOperationResponseDto'
        '400':
          description: Bad request - Invalid input data or minimum question violation
        '403':
          description: Forbidden - Insufficient permissions or school access denied
        '404':
          description: Worksheet not found
      security: *ref_13
      summary: Bulk remove questions from a worksheet
      tags: *ref_14
  /worksheets/{id}/questions/bulk-update:
    patch:
      description: >-
        Update multiple questions in a worksheet in a single operation with
        comprehensive validation and access control.

            **Access Control:**
            - **Admin users**: Can update questions in any worksheet
            - **School Manager**: Can update questions in worksheets in their school only
            - **Teacher**: Can update questions in worksheets in their school only
            - **Independent Teacher**: Can update questions in their own worksheets only

            **Features:**
            - Supports updating up to 50 questions at once
            - Validates each update before applying
            - Supports optimistic locking with version numbers
            - Implements transaction-like behavior (partial success allowed)
            - Updates worksheet metadata and cache
            - Emits real-time updates via WebSocket
            - Comprehensive audit logging
            
      operationId: WorksheetController_bulkUpdateQuestions
      parameters:
        - name: id
          required: true
          in: path
          description: Worksheet ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkUpdateQuestionsDto'
      responses:
        '200':
          description: Bulk update operation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkOperationResponseDto'
        '400':
          description: Bad request - Invalid input data
        '403':
          description: Forbidden - Insufficient permissions or school access denied
        '404':
          description: Worksheet not found
        '409':
          description: Conflict - Version mismatch (optimistic locking)
      security: *ref_13
      summary: Bulk update questions in a worksheet
      tags: *ref_14
  /worksheets/{id}:
    get:
      description: |-
        Retrieves a specific worksheet by ID with school-based access control.

            **Access Control:**
            - **Admin users**: Can access worksheets from any school
            - **Non-admin users**: Can only access worksheets from their own school
            - If a non-admin user attempts to access a worksheet from a different school, a 404 Not Found error is returned (to avoid revealing the existence of the worksheet)
      operationId: WorksheetController_findOne
      parameters:
        - name: id
          required: true
          in: path
          description: Worksheet ID (UUID format)
          schema:
            type: string
      responses:
        '200':
          description: Worksheet retrieved successfully
        '401':
          description: Unauthorized - Missing or invalid authentication token
        '404':
          description: Worksheet not found or user does not have access to this worksheet
      security: *ref_13
      summary: Get worksheet by ID with school-based access control
      tags: *ref_14
    delete:
      operationId: WorksheetController_remove
      parameters:
        - name: id
          required: true
          in: path
          description: Worksheet ID
          schema:
            type: string
      responses:
        '204':
          description: Worksheet deleted successfully
        '403':
          description: Forbidden. User does not have permission to delete this worksheet.
        '404':
          description: Worksheet not found.
      security: *ref_13
      summary: Delete a worksheet
      tags: *ref_14
  /worksheets/questions/metrics:
    get:
      description: Returns metrics in Prometheus format for scraping by monitoring systems
      operationId: WorksheetQuestionMetricsController_getPrometheusMetrics
      parameters: []
      responses:
        '200':
          description: Metrics retrieved successfully
          content:
            text/plain:
              schema:
                type: string
                example: >-
                  # HELP worksheet_question_api_duration_seconds Duration of
                  worksheet question API operations in seconds

                  # TYPE worksheet_question_api_duration_seconds histogram

                  worksheet_question_api_duration_seconds_bucket{le="0.001",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  0

                  worksheet_question_api_duration_seconds_bucket{le="0.005",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  0

                  worksheet_question_api_duration_seconds_bucket{le="0.01",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  5

                  worksheet_question_api_duration_seconds_bucket{le="0.05",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  15

                  worksheet_question_api_duration_seconds_bucket{le="0.1",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  25

                  worksheet_question_api_duration_seconds_bucket{le="0.5",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  30

                  worksheet_question_api_duration_seconds_bucket{le="1",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  30

                  worksheet_question_api_duration_seconds_bucket{le="2",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  30

                  worksheet_question_api_duration_seconds_bucket{le="5",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  30

                  worksheet_question_api_duration_seconds_bucket{le="10",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  30

                  worksheet_question_api_duration_seconds_bucket{le="+Inf",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  30

                  worksheet_question_api_duration_seconds_sum{method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  0.75

                  worksheet_question_api_duration_seconds_count{method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"}
                  30
      security: &ref_15
        - bearer: []
      summary: Get Prometheus metrics for worksheet question operations
      tags: &ref_16
        - Worksheet Question Metrics
  /worksheets/questions/metrics/summary:
    get:
      description: >-
        Returns a JSON summary with key performance indicators for dashboard
        display
      operationId: WorksheetQuestionMetricsController_getPerformanceSummary
      parameters: []
      responses:
        '200':
          description: Performance summary retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  totalApiRequests:
                    type: number
                    description: Total number of API requests processed
                    example: 1250
                  totalApiErrors:
                    type: number
                    description: Total number of API errors encountered
                    example: 15
                  averageApiDuration:
                    type: number
                    description: Average API response time in seconds
                    example: 0.045
                  cacheHitRate:
                    type: number
                    description: Cache hit rate as a percentage
                    example: 75.5
                  activeOperations:
                    type: number
                    description: Number of currently active operations
                    example: 8
                  memoryUsage:
                    type: number
                    description: Current memory usage in bytes
                    example: 134217728
                  timestamp:
                    type: string
                    format: date-time
                    description: Timestamp when metrics were collected
                    example: '2024-01-15T10:30:00.000Z'
        '403':
          description: Forbidden - Insufficient permissions
      security: *ref_15
      summary: Get performance summary for worksheet question operations
      tags: *ref_16
  /worksheets/questions/metrics/detailed:
    get:
      description: >-
        Returns detailed performance metrics for administrative analysis and
        troubleshooting
      operationId: WorksheetQuestionMetricsController_getDetailedMetrics
      parameters: []
      responses:
        '200':
          description: Detailed metrics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  apiMetrics:
                    type: object
                    properties:
                      requestsByEndpoint:
                        type: object
                        description: Request counts grouped by endpoint
                      errorsByType:
                        type: object
                        description: Error counts grouped by error type
                      responseTimePercentiles:
                        type: object
                        description: Response time percentiles (P50, P95, P99)
                  cacheMetrics:
                    type: object
                    properties:
                      hitRateByType:
                        type: object
                        description: Cache hit rates by cache type
                      operationDurations:
                        type: object
                        description: Cache operation durations
                  databaseMetrics:
                    type: object
                    properties:
                      queryDurations:
                        type: object
                        description: Database query durations by operation
                      connectionPoolStatus:
                        type: object
                        description: Database connection pool status
                  collaborationMetrics:
                    type: object
                    properties:
                      eventsByType:
                        type: object
                        description: Collaboration events by type
                      activeCollaboratorsByWorksheet:
                        type: object
                        description: Active collaborators per worksheet
        '403':
          description: Forbidden - Admin access required
      security: *ref_15
      summary: Get detailed performance breakdown for worksheet question operations
      tags: *ref_16
  /worksheets/cache/statistics:
    get:
      description: >-
        Returns detailed statistics about cache usage, hit ratios, and
        performance metrics
      operationId: WorksheetCacheManagementController_getCacheStatistics
      parameters: []
      responses:
        '200':
          description: Cache statistics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  redis:
                    type: object
                    properties:
                      questionMetadata:
                        type: number
                        description: Number of cached question metadata entries
                      userPermissions:
                        type: number
                        description: Number of cached user permission entries
                      worksheetSummaries:
                        type: number
                        description: Number of cached worksheet summaries
                      popularWorksheets:
                        type: number
                        description: Number of cached popular worksheet lists
                      collaborationStates:
                        type: number
                        description: Number of cached collaboration states
                      totalKeys:
                        type: number
                        description: Total number of cache keys
                      memoryUsage:
                        type: object
                        description: Redis memory usage information
                      health:
                        type: object
                        description: Cache health status
                  enhanced:
                    type: object
                    properties:
                      totalCachedWorksheets:
                        type: number
                      totalMetadataEntries:
                        type: number
                      totalPermissionEntries:
                        type: number
                      cacheConfiguration:
                        type: object
                  performance:
                    type: object
                    properties:
                      hitRate:
                        type: number
                        description: Overall cache hit rate percentage
                      averageResponseTime:
                        type: number
                        description: Average cache response time in ms
      security: *ref_17
      summary: Get comprehensive cache statistics
      tags: *ref_18
  /worksheets/cache/health:
    get:
      description: Returns health status and diagnostics for all cache layers
      operationId: WorksheetCacheManagementController_getCacheHealth
      parameters: []
      responses:
        '200':
          description: Cache health status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  overall:
                    type: string
                    enum:
                      - healthy
                      - warning
                      - critical
                  redis:
                    type: object
                    description: Redis cache health
                  enhanced:
                    type: object
                    description: Enhanced cache health
                  recommendations:
                    type: array
                    items:
                      type: string
      security: *ref_17
      summary: Get cache health status
      tags: *ref_18
  /worksheets/cache/worksheet/{worksheetId}:
    delete:
      description: >-
        Removes all cached data related to a specific worksheet from all cache
        layers
      operationId: WorksheetCacheManagementController_invalidateWorksheetCache
      parameters:
        - name: worksheetId
          required: true
          in: path
          description: ID of the worksheet to invalidate cache for
          schema:
            type: string
      responses:
        '204':
          description: Cache invalidated successfully
        '404':
          description: Worksheet not found
      security: *ref_17
      summary: Invalidate cache for a specific worksheet
      tags: *ref_18
  /worksheets/cache/popular/{schoolId}:
    get:
      description: Returns cached list of popular worksheets for the specified school
      operationId: WorksheetCacheManagementController_getPopularWorksheets
      parameters:
        - name: schoolId
          required: true
          in: path
          description: ID of the school
          schema:
            type: string
      responses:
        '200':
          description: Popular worksheets retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  worksheets:
                    type: array
                    items:
                      type: object
                  cached:
                    type: boolean
                    description: Whether data was served from cache
                  cacheAge:
                    type: string
                    description: Age of cached data
      security: *ref_17
      summary: Get popular worksheets for a school
      tags: *ref_18
  /worksheets/cache/recent/{userId}:
    get:
      description: >-
        Returns cached list of recently accessed worksheets for the specified
        user
      operationId: WorksheetCacheManagementController_getUserRecentWorksheets
      parameters:
        - name: userId
          required: true
          in: path
          description: ID of the user
          schema:
            type: string
      responses:
        '200':
          description: Recent worksheets retrieved successfully
      security: *ref_17
      summary: Get user's recent worksheets from cache
      tags: *ref_18
  /worksheets/cache/collaboration/{worksheetId}:
    get:
      description: >-
        Returns real-time collaboration state including active users and locked
        questions
      operationId: WorksheetCacheManagementController_getCollaborationState
      parameters:
        - name: worksheetId
          required: true
          in: path
          description: ID of the worksheet
          schema:
            type: string
      responses:
        '200':
          description: Collaboration state retrieved successfully
      security: *ref_17
      summary: Get collaboration state for a worksheet
      tags: *ref_18
  /worksheets/cache/clear-all:
    delete:
      description: >-
        Clears all cached data for worksheet questions. Use with caution as this
        will impact performance temporarily.
      operationId: WorksheetCacheManagementController_clearAllCache
      parameters: []
      responses:
        '200':
          description: All cache cleared successfully
        '403':
          description: Forbidden - Admin access required
      security: *ref_17
      summary: Clear all worksheet cache (emergency operation)
      tags: *ref_18
  /worksheets/jobs/bulk-add:
    post:
      description: Queues a background job to add multiple questions to a worksheet
      operationId: WorksheetBackgroundJobsController_queueBulkAdd
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                worksheetId:
                  type: string
                  description: ID of the worksheet
                questions:
                  type: array
                  items:
                    type: object
                  description: Array of questions to add
              required:
                - worksheetId
                - questions
      responses:
        '201':
          description: Bulk add job queued successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  jobId:
                    type: string
                    description: ID of the queued job
                  message:
                    type: string
                  estimatedDuration:
                    type: string
      security: &ref_19
        - bearer: []
      summary: Queue bulk add operation for worksheet questions
      tags: &ref_20
        - Worksheet Background Jobs
  /worksheets/jobs/bulk-update:
    post:
      description: Queues a background job to update multiple questions in a worksheet
      operationId: WorksheetBackgroundJobsController_queueBulkUpdate
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                worksheetId:
                  type: string
                  description: ID of the worksheet
                updates:
                  type: array
                  items:
                    type: object
                    properties:
                      questionId:
                        type: string
                      updates:
                        type: object
                  description: Array of question updates
              required:
                - worksheetId
                - updates
      responses:
        '201':
          description: Bulk update job queued successfully
      security: *ref_19
      summary: Queue bulk update operation for worksheet questions
      tags: *ref_20
  /worksheets/jobs/bulk-delete:
    post:
      description: Queues a background job to delete multiple questions from a worksheet
      operationId: WorksheetBackgroundJobsController_queueBulkDelete
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                worksheetId:
                  type: string
                  description: ID of the worksheet
                questionIds:
                  type: array
                  items:
                    type: string
                  description: Array of question IDs to delete
              required:
                - worksheetId
                - questionIds
      responses:
        '201':
          description: Bulk delete job queued successfully
      security: *ref_19
      summary: Queue bulk delete operation for worksheet questions
      tags: *ref_20
  /worksheets/jobs/export:
    post:
      description: Queues a background job to export a worksheet in the specified format
      operationId: WorksheetBackgroundJobsController_queueExport
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                worksheetId:
                  type: string
                  description: ID of the worksheet
                format:
                  type: string
                  enum:
                    - pdf
                    - docx
                    - json
                    - csv
                  description: Export format
                options:
                  type: object
                  description: Export options
              required:
                - worksheetId
                - format
      responses:
        '201':
          description: Export job queued successfully
      security: *ref_19
      summary: Queue worksheet export operation
      tags: *ref_20
  /worksheets/jobs/{jobId}/progress:
    get:
      description: Returns the current progress and status of a background job
      operationId: WorksheetBackgroundJobsController_getJobProgress
      parameters:
        - name: jobId
          required: true
          in: path
          description: ID of the job
          schema:
            type: string
      responses:
        '200':
          description: Job progress retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  jobId:
                    type: string
                  type:
                    type: string
                  status:
                    type: string
                    enum:
                      - queued
                      - active
                      - completed
                      - failed
                      - delayed
                  progress:
                    type: number
                  total:
                    type: number
                  currentItem:
                    type: string
                  startedAt:
                    type: string
                    format: date-time
                  completedAt:
                    type: string
                    format: date-time
                  error:
                    type: string
                  result:
                    type: object
        '404':
          description: Job not found
      security: *ref_19
      summary: Get job progress
      tags: *ref_20
  /worksheets/jobs/active:
    get:
      description: Returns all active background jobs for the current user
      operationId: WorksheetBackgroundJobsController_getUserActiveJobs
      parameters: []
      responses:
        '200':
          description: Active jobs retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  jobs:
                    type: array
                    items:
                      type: object
                      properties:
                        jobId:
                          type: string
                        type:
                          type: string
                        status:
                          type: string
                        progress:
                          type: number
                        total:
                          type: number
                  totalJobs:
                    type: number
      security: *ref_19
      summary: Get user's active jobs
      tags: *ref_20
  /worksheets/jobs/{jobId}:
    delete:
      description: Cancels a queued or active background job
      operationId: WorksheetBackgroundJobsController_cancelJob
      parameters:
        - name: jobId
          required: true
          in: path
          description: ID of the job to cancel
          schema:
            type: string
      responses:
        '204':
          description: Job cancelled successfully
        '403':
          description: Unauthorized to cancel this job
        '404':
          description: Job not found
      security: *ref_19
      summary: Cancel a background job
      tags: *ref_20
  /worksheets/jobs/statistics:
    get:
      description: Returns statistics about the background job queue (Admin only)
      operationId: WorksheetBackgroundJobsController_getQueueStatistics
      parameters: []
      responses:
        '200':
          description: Queue statistics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  waiting:
                    type: number
                    description: Number of waiting jobs
                  active:
                    type: number
                    description: Number of active jobs
                  completed:
                    type: number
                    description: Number of completed jobs
                  failed:
                    type: number
                    description: Number of failed jobs
                  delayed:
                    type: number
                    description: Number of delayed jobs
                  total:
                    type: number
                    description: Total number of jobs
      security: *ref_19
      summary: Get queue statistics
      tags: *ref_20
  /worksheets/memory/{worksheetId}/questions/paginated:
    get:
      description: >-
        Returns paginated worksheet questions with memory usage tracking and
        optimization
      operationId: WorksheetMemoryOptimizationController_getPaginatedQuestions
      parameters:
        - name: worksheetId
          required: true
          in: path
          description: ID of the worksheet
          schema:
            type: string
        - name: page
          required: false
          in: query
          description: 'Page number (default: 1)'
          schema:
            type: number
        - name: limit
          required: false
          in: query
          description: 'Items per page (default: 20, max: 100)'
          schema:
            type: number
        - name: sortBy
          required: false
          in: query
          description: 'Field to sort by (default: position)'
          schema:
            type: string
        - name: sortOrder
          required: false
          in: query
          description: 'Sort order (default: asc)'
          schema:
            enum:
              - asc
              - desc
            type: string
      responses:
        '200':
          description: Paginated questions retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                    description: Array of questions for the current page
                  pagination:
                    type: object
                    properties:
                      page:
                        type: number
                      limit:
                        type: number
                      total:
                        type: number
                      totalPages:
                        type: number
                      hasNext:
                        type: boolean
                      hasPrev:
                        type: boolean
                  memoryUsage:
                    type: object
                    properties:
                      heapUsed:
                        type: number
                        description: Heap memory used in bytes
                      heapTotal:
                        type: number
                        description: Total heap memory in bytes
                      external:
                        type: number
                        description: External memory in bytes
                      rss:
                        type: number
                        description: Resident set size in bytes
      security: &ref_21
        - bearer: []
      summary: Get paginated worksheet questions with memory optimization
      tags: &ref_22
        - Worksheet Memory Optimization
  /worksheets/memory/{worksheetId}/questions/stream:
    get:
      description: >-
        Returns a streaming response for memory-efficient export of large
        worksheets
      operationId: WorksheetMemoryOptimizationController_streamQuestions
      parameters:
        - name: worksheetId
          required: true
          in: path
          description: ID of the worksheet
          schema:
            type: string
        - name: format
          required: false
          in: query
          description: 'Export format (default: json)'
          schema:
            enum:
              - json
              - csv
            type: string
        - name: batchSize
          required: false
          in: query
          description: 'Batch size for streaming (default: 50)'
          schema:
            type: number
      responses:
        '200':
          description: Streaming export started successfully
          content:
            application/json:
              schema:
                type: string
                description: Streaming JSON data
            text/csv:
              schema:
                type: string
                description: Streaming CSV data
      security: *ref_21
      summary: Stream worksheet questions for large exports
      tags: *ref_22
  /worksheets/memory/{worksheetId}/questions/{questionId}/lazy:
    get:
      description: Returns only requested fields of a question to minimize memory usage
      operationId: WorksheetMemoryOptimizationController_lazyLoadQuestionDetails
      parameters:
        - name: worksheetId
          required: true
          in: path
          description: ID of the worksheet
          schema:
            type: string
        - name: questionId
          required: true
          in: path
          description: ID of the question
          schema:
            type: string
        - name: fields
          required: false
          in: query
          description: >-
            Comma-separated list of fields to load (e.g.,
            content,options,answer)
          schema:
            type: string
      responses:
        '200':
          description: Question details loaded successfully
          content:
            application/json:
              schema:
                type: object
                description: Partial question object with only requested fields
        '404':
          description: Question not found
      security: *ref_21
      summary: Lazy load specific question details
      tags: *ref_22
  /worksheets/memory/statistics:
    get:
      description: Returns current memory usage statistics and optimization metrics
      operationId: WorksheetMemoryOptimizationController_getMemoryStatistics
      parameters: []
      responses:
        '200':
          description: Memory statistics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  current:
                    type: object
                    properties:
                      heapUsed:
                        type: number
                        description: Current heap usage in MB
                      heapTotal:
                        type: number
                        description: Total heap size in MB
                      external:
                        type: number
                        description: External memory in MB
                      rss:
                        type: number
                        description: Resident set size in MB
                  thresholds:
                    type: object
                    properties:
                      warning:
                        type: number
                        description: Warning threshold in MB
                      critical:
                        type: number
                        description: Critical threshold in MB
                  alerts:
                    type: array
                    items:
                      type: object
                      properties:
                        operation:
                          type: string
                        alertCount:
                          type: number
                  configuration:
                    type: object
                    description: Memory optimization configuration
      security: *ref_21
      summary: Get memory usage statistics
      tags: *ref_22
  /worksheets/memory/cleanup:
    post:
      description: Manually triggers garbage collection and memory cleanup (Admin only)
      operationId: WorksheetMemoryOptimizationController_triggerMemoryCleanup
      parameters: []
      responses:
        '200':
          description: Memory cleanup completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  memoryBefore:
                    type: object
                  memoryAfter:
                    type: object
                  cleaned:
                    type: boolean
      security: *ref_21
      summary: Trigger memory cleanup
      tags: *ref_22
  /worksheets/memory/recommendations:
    get:
      description: >-
        Returns recommendations for improving memory usage based on current
        patterns
      operationId: WorksheetMemoryOptimizationController_getMemoryRecommendations
      parameters: []
      responses:
        '200':
          description: Recommendations retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  recommendations:
                    type: array
                    items:
                      type: object
                      properties:
                        category:
                          type: string
                        priority:
                          type: string
                          enum:
                            - low
                            - medium
                            - high
                        description:
                          type: string
                        action:
                          type: string
                  currentUsage:
                    type: object
                  projectedSavings:
                    type: object
      security: *ref_21
      summary: Get memory optimization recommendations
      tags: *ref_22
  /worksheet-questions/capacity-planning/report:
    get:
      description: >-
        Generates a comprehensive capacity planning report including current
        metrics, bottlenecks, recommendations, and cost projections
      operationId: >-
        WorksheetQuestionCapacityPlanningController_generateCapacityPlanningReport
      parameters: []
      responses:
        '200':
          description: Capacity planning report generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  timestamp:
                    type: string
                    format: date-time
                  currentCapacity:
                    type: object
                    properties:
                      currentLoad:
                        type: object
                        properties:
                          requestsPerSecond:
                            type: number
                          averageResponseTime:
                            type: number
                          errorRate:
                            type: number
                          memoryUsage:
                            type: number
                          cpuUsage:
                            type: number
                      projectedLoad:
                        type: object
                        properties:
                          expectedGrowthRate:
                            type: number
                          timeHorizon:
                            type: number
                          projectedRequestsPerSecond:
                            type: number
                          projectedMemoryUsage:
                            type: number
                      systemLimits:
                        type: object
                        properties:
                          maxRequestsPerSecond:
                            type: number
                          maxMemoryUsage:
                            type: number
                          maxResponseTime:
                            type: number
                          maxErrorRate:
                            type: number
                  bottlenecks:
                    type: array
                    items:
                      type: string
                  recommendations:
                    type: array
                    items:
                      type: object
                      properties:
                        priority:
                          type: string
                          enum:
                            - low
                            - medium
                            - high
                            - critical
                        category:
                          type: string
                          enum:
                            - scaling
                            - optimization
                            - infrastructure
                            - monitoring
                        title:
                          type: string
                        description:
                          type: string
                        impact:
                          type: string
                        effort:
                          type: string
                          enum:
                            - low
                            - medium
                            - high
                        timeline:
                          type: string
                        implementation:
                          type: array
                          items:
                            type: string
                  scalingPlan:
                    type: object
                    properties:
                      shortTerm:
                        type: array
                        items:
                          $ref: '#/components/schemas/CapacityRecommendation'
                      mediumTerm:
                        type: array
                        items:
                          $ref: '#/components/schemas/CapacityRecommendation'
                      longTerm:
                        type: array
                        items:
                          $ref: '#/components/schemas/CapacityRecommendation'
                  costProjections:
                    type: object
                    properties:
                      currentMonthlyCost:
                        type: number
                      projectedMonthlyCost:
                        type: number
                      optimizationSavings:
                        type: number
        '403':
          description: Forbidden - Admin or School Manager access required
        '500':
          description: Internal server error
      security: &ref_23
        - bearer: []
      summary: Generate capacity planning report
      tags: &ref_24
        - Worksheet Question Capacity Planning
  /worksheet-questions/capacity-planning/metrics:
    get:
      description: >-
        Returns current system capacity metrics including load, performance, and
        resource utilization
      operationId: WorksheetQuestionCapacityPlanningController_getCurrentCapacityMetrics
      parameters: []
      responses:
        '200':
          description: Current capacity metrics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  currentLoad:
                    type: object
                    properties:
                      requestsPerSecond:
                        type: number
                        description: Current requests per second
                      averageResponseTime:
                        type: number
                        description: Average response time in milliseconds
                      errorRate:
                        type: number
                        description: Error rate percentage
                      memoryUsage:
                        type: number
                        description: Memory usage percentage
                      cpuUsage:
                        type: number
                        description: CPU usage percentage
                  systemHealth:
                    type: object
                    properties:
                      status:
                        type: string
                        enum:
                          - healthy
                          - warning
                          - critical
                      issues:
                        type: array
                        items:
                          type: string
                      recommendations:
                        type: array
                        items:
                          type: string
                  timestamp:
                    type: string
                    format: date-time
        '403':
          description: Forbidden - Admin or School Manager access required
      security: *ref_23
      summary: Get current capacity metrics
      tags: *ref_24
  /worksheet-questions/capacity-planning/recommendations:
    get:
      description: >-
        Returns prioritized recommendations for capacity planning and
        performance optimization
      operationId: >-
        WorksheetQuestionCapacityPlanningController_getCapacityPlanningRecommendations
      parameters: []
      responses:
        '200':
          description: Capacity planning recommendations retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  recommendations:
                    type: array
                    items:
                      type: object
                      properties:
                        priority:
                          type: string
                          enum:
                            - low
                            - medium
                            - high
                            - critical
                        category:
                          type: string
                          enum:
                            - scaling
                            - optimization
                            - infrastructure
                            - monitoring
                        title:
                          type: string
                        description:
                          type: string
                        impact:
                          type: string
                        effort:
                          type: string
                          enum:
                            - low
                            - medium
                            - high
                        timeline:
                          type: string
                        implementation:
                          type: array
                          items:
                            type: string
                  prioritizedActions:
                    type: object
                    properties:
                      immediate:
                        type: array
                        items:
                          type: string
                      shortTerm:
                        type: array
                        items:
                          type: string
                      longTerm:
                        type: array
                        items:
                          type: string
                  timestamp:
                    type: string
                    format: date-time
        '403':
          description: Forbidden - Admin or School Manager access required
      security: *ref_23
      summary: Get capacity planning recommendations
      tags: *ref_24
  /worksheet-questions/capacity-planning/cost-projections:
    get:
      description: Returns cost projections based on current usage and growth trends
      operationId: WorksheetQuestionCapacityPlanningController_getCostProjections
      parameters: []
      responses:
        '200':
          description: Cost projections retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  currentMonthlyCost:
                    type: number
                    description: Current monthly cost in USD
                  projectedMonthlyCost:
                    type: number
                    description: Projected monthly cost in USD
                  optimizationSavings:
                    type: number
                    description: Potential savings from optimizations in USD
                  breakdown:
                    type: object
                    properties:
                      infrastructure:
                        type: number
                      storage:
                        type: number
                      bandwidth:
                        type: number
                      monitoring:
                        type: number
                  recommendations:
                    type: array
                    items:
                      type: string
                  timestamp:
                    type: string
                    format: date-time
        '403':
          description: Forbidden - Admin or School Manager access required
      security: *ref_23
      summary: Get cost projections
      tags: *ref_24
  /admin/monitoring/dashboard:
    get:
      description: >-
        Returns aggregated metrics including pool utilization, question reuse,
        generation times, validation rates, and cache performance. Requires
        admin role.
      operationId: MonitoringController_getDashboardMetrics
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the metrics range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the metrics range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            default: daily
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
      responses:
        '200':
          description: Dashboard metrics successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardMetricsResponseDto'
        '400':
          description: Bad Request - Invalid query parameters
        '401':
          description: Unauthorized - Invalid or missing token
        '403':
          description: Forbidden - Admin role required
      security: &ref_25
        - bearer: []
      summary: Get comprehensive dashboard metrics for question pool monitoring
      tags: &ref_26
        - Monitoring & Analytics
  /admin/monitoring/pool-utilization:
    get:
      description: >-
        Returns metrics about how effectively the question pool is being
        utilized
      operationId: MonitoringController_getPoolUtilization
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the metrics range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the metrics range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            default: daily
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
      responses:
        '200':
          description: Pool utilization metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PoolUtilizationResponseDto'
      security: *ref_25
      summary: Get pool utilization metrics
      tags: *ref_26
  /admin/monitoring/question-reuse:
    get:
      description: Returns metrics about how frequently questions are being reused
      operationId: MonitoringController_getQuestionReuse
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the metrics range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the metrics range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            default: daily
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
      responses:
        '200':
          description: Question reuse metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionReuseResponseDto'
      security: *ref_25
      summary: Get question reuse frequency metrics
      tags: *ref_26
  /admin/monitoring/generation-times:
    get:
      description: >-
        Returns comparison of generation times between pool selection and AI
        generation
      operationId: MonitoringController_getGenerationTimes
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the metrics range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the metrics range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            default: daily
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
      responses:
        '200':
          description: Generation time comparison metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerationTimeResponseDto'
      security: *ref_25
      summary: Get generation time comparison metrics
      tags: *ref_26
  /admin/monitoring/validation-metrics:
    get:
      description: >-
        Returns metrics about content validation success rates and issue
        breakdown
      operationId: MonitoringController_getValidationMetrics
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the metrics range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the metrics range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            default: daily
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
      responses:
        '200':
          description: Validation metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationResponseDto'
      security: *ref_25
      summary: Get validation success rate metrics
      tags: *ref_26
  /admin/monitoring/cache-metrics:
    get:
      description: >-
        Returns cache hit/miss ratios and performance metrics for specified
        cache type
      operationId: MonitoringController_getCacheMetrics
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the metrics range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the metrics range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            default: daily
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
        - name: cacheType
          required: false
          in: query
          description: Cache type to filter by
          schema:
            enum:
              - question_pool
              - worksheet_document
              - query_cache
            type: string
      responses:
        '200':
          description: Cache performance metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CacheResponseDto'
      security: *ref_25
      summary: Get cache performance metrics
      tags: *ref_26
  /admin/monitoring/events:
    get:
      description: >-
        Returns raw monitoring events for detailed analysis. Use with caution as
        this can return large datasets.
      operationId: MonitoringController_getEvents
      parameters:
        - name: startDate
          required: false
          in: query
          description: Start date for the query range (ISO string)
          schema:
            example: '2024-01-01T00:00:00.000Z'
            type: string
        - name: endDate
          required: false
          in: query
          description: End date for the query range (ISO string)
          schema:
            example: '2024-01-31T23:59:59.999Z'
            type: string
        - name: timeframe
          required: false
          in: query
          description: Timeframe for aggregated metrics
          schema:
            example: daily
            type: string
            enum:
              - hourly
              - daily
              - weekly
              - monthly
        - name: eventType
          required: false
          in: query
          description: Filter by specific event type
          schema:
            example: question_selection
            type: string
            enum:
              - question_selection
              - question_generation
              - validation_attempt
              - cache_interaction
              - worksheet_generation
              - distribution_analysis
        - name: userId
          required: false
          in: query
          description: Filter by user ID
          schema:
            example: user-123
            type: string
        - name: worksheetId
          required: false
          in: query
          description: Filter by worksheet ID
          schema:
            example: worksheet-456
            type: string
        - name: limit
          required: false
          in: query
          description: Maximum number of events to return
          schema:
            minimum: 1
            maximum: 10000
            example: 1000
            type: number
      responses:
        '200':
          description: Raw monitoring events
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    eventId:
                      type: string
                    type:
                      type: string
                    timestamp:
                      type: string
                      format: date-time
                    userId:
                      type: string
                    worksheetId:
                      type: string
                    eventData:
                      type: object
      security: *ref_25
      summary: Get raw monitoring events
      tags: *ref_26
  /options/types:
    post:
      operationId: OptionsController_createOptionType
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOptionTypeDto'
      responses:
        '201':
          description: Option type created successfully
        '400':
          description: Bad request
      summary: Create a new option type
      tags: &ref_27
        - Options
    get:
      operationId: OptionsController_findAllOptionTypes
      parameters: []
      responses:
        '200':
          description: Option types retrieved successfully
      summary: Get all option types
      tags: *ref_27
  /options/values:
    post:
      operationId: OptionsController_createOptionValue
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOptionValueDto'
      responses:
        '201':
          description: Option value created successfully
        '400':
          description: Bad request
      summary: Create a new option value
      tags: *ref_27
  /options/types/by-key/{key}:
    get:
      operationId: OptionsController_findOptionTypeByKey
      parameters:
        - name: key
          required: true
          in: path
          description: Option type key
          schema:
            type: string
      responses:
        '200':
          description: Option type retrieved successfully
        '404':
          description: Option type not found
      summary: Get option type by key
      tags: *ref_27
  /options/types/{id}:
    get:
      operationId: OptionsController_findOptionTypeById
      parameters:
        - name: id
          required: true
          in: path
          description: Option type ID
          schema:
            type: string
      responses:
        '200':
          description: Option type retrieved successfully
        '404':
          description: Option type not found
      summary: Get option type by ID
      tags: *ref_27
  /options/values/by-type/{typeId}:
    get:
      operationId: OptionsController_findOptionValuesByTypeId
      parameters:
        - name: typeId
          required: true
          in: path
          description: Option type ID
          schema:
            type: string
      responses:
        '200':
          description: Option values retrieved successfully
        '404':
          description: Option type not found
      summary: Get option values by type ID
      tags: *ref_27
  /options/subjects:
    post:
      operationId: OptionsController_createSubject
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSubjectDto'
      responses:
        '201':
          description: Subject created successfully
        '400':
          description: Bad request
      summary: Create a new subject
      tags: *ref_27
    get:
      operationId: OptionsController_findAllSubjects
      parameters: []
      responses:
        '200':
          description: Subjects retrieved successfully
      summary: Get all subjects
      tags: *ref_27
  /options/subjects/hierarchy:
    get:
      operationId: OptionsController_getSubjectHierarchy
      parameters:
        - name: topicId
          required: false
          in: query
          description: Filter by topic ID
          schema:
            example: 123e4567-e89b-12d3-a456-************
            type: string
        - name: gradeId
          required: false
          in: query
          description: Filter by grade ID
          schema:
            example: 123e4567-e89b-12d3-a456-************
            type: string
      responses:
        '200':
          description: Subject hierarchy retrieved successfully
        '404':
          description: Topic not found
      summary: Get complete subject hierarchy
      tags: *ref_27
  /options/subjects/by-parent/{parentId}:
    get:
      operationId: OptionsController_findSubjectsByParentId
      parameters:
        - name: parentId
          required: true
          in: path
          description: Parent subject ID
          schema:
            type: string
      responses:
        '200':
          description: Subjects retrieved successfully
      summary: Get subjects by parent ID
      tags: *ref_27
  /options/subjects/root:
    get:
      operationId: OptionsController_findRootSubjects
      parameters: []
      responses:
        '200':
          description: Root subjects retrieved successfully
      summary: Get root subjects (without parent)
      tags: *ref_27
  /options/subjects/by-topic/{topicId}:
    get:
      operationId: OptionsController_findSubjectsByTopicId
      parameters:
        - name: topicId
          required: true
          in: path
          description: Topic ID
          schema:
            type: string
        - name: type
          required: false
          in: query
          description: Filter by subject type
          schema:
            enum:
              - parent
              - child
            type: string
      responses:
        '200':
          description: Subjects retrieved successfully
        '404':
          description: Topic not found
      summary: Get subjects by topic ID
      tags: *ref_27
  /options/subjects/{id}:
    get:
      operationId: OptionsController_findSubjectById
      parameters:
        - name: id
          required: true
          in: path
          description: Subject ID
          schema:
            type: string
      responses:
        '200':
          description: Subject retrieved successfully
        '404':
          description: Subject not found
      summary: Get subject by ID
      tags: *ref_27
    put:
      operationId: OptionsController_updateSubject
      parameters:
        - name: id
          required: true
          in: path
          description: Subject ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSubjectDto'
      responses:
        '200':
          description: Subject updated successfully
        '404':
          description: Subject not found
      summary: Update a subject
      tags: *ref_27
    delete:
      operationId: OptionsController_deleteSubject
      parameters:
        - name: id
          required: true
          in: path
          description: Subject ID
          schema:
            type: string
      responses:
        '200':
          description: Subject deleted successfully
        '404':
          description: Subject not found
      summary: Delete a subject
      tags: *ref_27
  /options/values/{id}:
    delete:
      operationId: OptionsController_deleteOptionValue
      parameters:
        - name: id
          required: true
          in: path
          description: Option value ID
          schema:
            type: string
      responses:
        '204':
          description: Option value deleted successfully
        '400':
          description: Bad request
        '404':
          description: Option value not found
      summary: Delete an option value
      tags: *ref_27
  /options/seed-initial-data:
    post:
      operationId: OptionsController_seedInitialData
      parameters: []
      responses:
        '201':
          description: Initial data seeded successfully
        '400':
          description: Bad request
      summary: Seed initial option data
      tags: *ref_27
  /options/seed-math-subjects:
    post:
      operationId: OptionsController_seedMathSubjects
      parameters: []
      responses:
        '201':
          description: Math subjects seeded successfully
        '400':
          description: Bad request
      summary: Seed math subjects data for P5 and P6
      tags: *ref_27
  /options/seed-koobits-p5-math:
    post:
      operationId: OptionsController_seedKooBitsP5Math
      parameters: []
      responses:
        '201':
          description: KooBits P5 math subjects seeded successfully
        '400':
          description: Bad request
      summary: Seed P5 math subjects from KooBits curriculum
      tags: *ref_27
  /options/seed-p5-math-subjects-detailed:
    post:
      operationId: OptionsController_seedP5MathSubjectsDetailed
      parameters: []
      responses:
        '201':
          description: Detailed P5 math subjects seeded successfully
        '400':
          description: Bad request
      summary: Seed detailed P5 math subjects with all sub-topics
      tags: *ref_27
  /exams:
    post:
      operationId: ExamController_createExam
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateExamDto'
      responses:
        '201':
          description: Exam created successfully
        '400':
          description: Bad request
        '403':
          description: Forbidden - You already have an unfinished exam for this worksheet
      security: &ref_28
        - bearer: []
      summary: Create a new exam
      tags: &ref_29
        - Exams
  /exams/{id}:
    get:
      operationId: ExamController_getExam
      parameters:
        - name: id
          required: true
          in: path
          description: Exam ID
          schema:
            type: string
      responses:
        '200':
          description: Exam retrieved successfully
        '404':
          description: Exam not found
      security: *ref_28
      summary: Get exam by ID
      tags: *ref_29
  /exams/{id}/submit:
    post:
      operationId: ExamController_submitExam
      parameters:
        - name: id
          required: true
          in: path
          description: Exam ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitExamDto'
      responses:
        '200':
          description: Exam submitted successfully
        '400':
          description: Bad request - You have already submitted this exam
        '404':
          description: Exam not found
      security: *ref_28
      summary: Submit exam answers
      tags: *ref_29
  /exams/by-worksheet/{worksheetId}:
    get:
      operationId: ExamController_getExamsByWorksheet
      parameters:
        - name: worksheetId
          required: true
          in: path
          description: Worksheet ID
          schema:
            type: string
      responses:
        '200':
          description: Exams retrieved successfully
      security: *ref_28
      summary: Get exams by worksheet ID
      tags: *ref_29
info:
  title: EduSG API
  description: The EduSG API documentation
  version: '1.0'
  contact: {}
tags: []
servers: []
components:
  securitySchemes:
    bearer:
      scheme: bearer
      bearerFormat: JWT
      type: http
  schemas:
    EUserRole:
      type: string
      enum:
        - teacher
        - admin
        - student
        - school_manager
        - independent_teacher
      description: User role
    CreateUserDto:
      type: object
      properties:
        name:
          type: string
          description: User full name
          example: John Doe
        email:
          type: string
          description: User email address
          example: <EMAIL>
        password:
          type: string
          description: User password (minimum 6 characters)
          example: password123
          minLength: 6
        role:
          description: User role
          example: teacher
          allOf:
            - $ref: '#/components/schemas/EUserRole'
        schoolId:
          type: object
          description: School ID (required for TEACHER, STUDENT, and SCHOOL_MANAGER roles)
          example: 123e4567-e89b-12d3-a456-************
      required:
        - name
        - email
        - password
        - role
    UpdateUserDto:
      type: object
      properties:
        name:
          type: string
          description: User full name
          example: John Doe
        email:
          type: string
          description: User email address
          example: <EMAIL>
        password:
          type: string
          description: User password (minimum 6 characters)
          example: password123
          minLength: 6
        role:
          description: User role
          example: teacher
          allOf:
            - $ref: '#/components/schemas/EUserRole'
        schoolId:
          type: string
          description: School ID (required for TEACHER, STUDENT, and SCHOOL_MANAGER roles)
          example: 123e4567-e89b-12d3-a456-************
    SignInDto:
      type: object
      properties:
        email:
          type: string
          description: User email address
          example: <EMAIL>
        password:
          type: string
          description: User password
          example: password123
      required:
        - email
        - password
    SignUpDto:
      type: object
      properties:
        role:
          type: string
          description: User role
          enum:
            - teacher
            - admin
            - student
            - school_manager
            - independent_teacher
          default: teacher
        schoolId:
          type: string
          description: >-
            School ID (required for TEACHER, STUDENT, and SCHOOL_MANAGER roles,
            must be null or omitted for INDEPENDENT_TEACHER and ADMIN)
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          description: User full name
          example: John Doe
        email:
          type: string
          description: User email address
          example: <EMAIL>
        password:
          type: string
          description: User password (minimum 6 characters)
          example: password123
          minLength: 6
      required:
        - role
        - name
        - email
        - password
    CreateSchoolDto:
      type: object
      properties:
        name:
          type: string
          description: The official name of the school
          example: Springfield Elementary School
        address:
          type: string
          description: The physical location of the school
          example: 123 Education St, Springfield
        phoneNumber:
          type: string
          description: Contact number for the school
          example: ******-123-4567
        registeredNumber:
          type: string
          description: Official registration or license number
          example: REG12345678
        email:
          type: string
          description: Official email address for communications
          example: <EMAIL>
        adminId:
          type: string
          description: ID of the school administrator (School Manager user)
          example: 123e4567-e89b-12d3-a456-************
        brandId:
          type: string
          description: ID of the associated brand
          example: 123e4567-e89b-12d3-a456-************
      required:
        - name
        - email
    User:
      type: object
      properties: {}
    Brand:
      type: object
      properties:
        logo:
          type: string
          description: The official logo or symbol representing the school
          example: https://example.com/logo.png
        color:
          type: string
          description: Brand color scheme or primary colors
          example: '#FF5733'
        image:
          type: string
          description: Additional brand imagery or visual assets
          example: https://example.com/image.png
      required:
        - logo
        - color
        - image
    School:
      type: object
      properties:
        name:
          type: string
          description: The official name of the school
          example: Springfield Elementary School
        address:
          type: string
          description: The physical location of the school
          example: 123 Education St, Springfield
        phoneNumber:
          type: string
          description: Contact number for the school
          example: ******-123-4567
        registeredNumber:
          type: string
          description: Official registration or license number
          example: REG12345678
        email:
          type: string
          description: Official email address for communications
          example: <EMAIL>
        admin:
          description: Reference to the school administrator (School Manager user)
          allOf:
            - $ref: '#/components/schemas/User'
        brand:
          description: Reference to the associated brand information
          allOf:
            - $ref: '#/components/schemas/Brand'
      required:
        - name
        - address
        - phoneNumber
        - email
        - admin
        - brand
    UpdateSchoolDto:
      type: object
      properties:
        name:
          type: string
          description: The official name of the school
          example: Springfield Elementary School
        address:
          type: string
          description: The physical location of the school
          example: 123 Education St, Springfield
        phoneNumber:
          type: string
          description: Contact number for the school
          example: ******-123-4567
        registeredNumber:
          type: string
          description: Official registration or license number
          example: REG12345678
        email:
          type: string
          description: Official email address for communications
          example: <EMAIL>
        adminId:
          type: string
          description: ID of the school administrator (School Manager user)
          example: 123e4567-e89b-12d3-a456-************
        brandId:
          type: string
          description: ID of the associated brand
          example: 123e4567-e89b-12d3-a456-************
    NarrativeStructureResponseDto:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the narrative structure
          example: 123e4567-e89b-12d3-a456-************
        schoolId:
          type: string
          description: The school ID this narrative structure belongs to
          example: 123e4567-e89b-12d3-a456-************
        content:
          type: string
          description: The extracted narrative structure content
          example: >-
            This school follows a structured examination format with emphasis
            on...
        sourceFormatsCount:
          type: number
          description: Number of examination formats analyzed
          example: 3
        extractedAt:
          format: date-time
          type: string
          description: Timestamp when the narrative structure was extracted
        version:
          type: number
          description: Version number of the narrative structure
          example: 1
        extractionMetadata:
          type: object
          description: Metadata about the extraction process
          example:
            aiModel: gpt-4
            processingTime: 1500
            confidence: 0.95
        createdAt:
          format: date-time
          type: string
          description: Creation timestamp
        updatedAt:
          format: date-time
          type: string
          description: Last update timestamp
      required:
        - id
        - schoolId
        - content
        - sourceFormatsCount
        - extractedAt
        - version
        - createdAt
        - updatedAt
    ExtractNarrativeStructureResponseDto:
      type: object
      properties:
        success:
          type: boolean
          description: Success status of the extraction
          example: true
        message:
          type: string
          description: Status message
          example: Narrative structure extracted successfully
        narrativeStructure:
          description: The extracted narrative structure
          allOf:
            - $ref: '#/components/schemas/NarrativeStructureResponseDto'
        processedFormatsCount:
          type: number
          description: Number of examination formats processed
          example: 3
      required:
        - success
        - message
    BulkExtractNarrativeStructureResponseDto:
      type: object
      properties:
        success:
          type: boolean
          description: Overall success status
          example: true
        message:
          type: string
          description: Overall status message
          example: Bulk narrative structure extraction completed
        totalSchools:
          type: number
          description: Total number of schools processed
          example: 10
        successfulExtractions:
          type: number
          description: Number of schools successfully processed
          example: 8
        failedExtractions:
          type: number
          description: Number of schools that failed processing
          example: 2
        results:
          description: Details of the extraction results per school
          example:
            - schoolId: '123'
              success: true
              message: Success
            - schoolId: '456'
              success: false
              message: No examination formats found
          type: array
          items:
            type: object
      required:
        - success
        - message
        - totalSchools
        - successfulExtractions
        - failedExtractions
        - results
    CreateBrandDto:
      type: object
      properties:
        logo:
          type: string
          description: The official logo or symbol representing the school
          example: https://example.com/logo.png
        color:
          type: string
          description: Brand color scheme or primary colors
          example: '#FF5733'
        image:
          type: string
          description: Additional brand imagery or visual assets
          example: https://example.com/image.png
    UpdateBrandDto:
      type: object
      properties:
        logo:
          type: string
          description: The official logo or symbol representing the school
          example: https://example.com/logo.png
        color:
          type: string
          description: Brand color scheme or primary colors
          example: '#FF5733'
        image:
          type: string
          description: Additional brand imagery or visual assets
          example: https://example.com/image.png
    SubjectDataItem:
      type: object
      properties:
        label:
          type: string
          description: Label of the subject
          example: Speed
        items:
          description: Items within the subject
          example:
            - Speed Conversion
            - Time, Distance and Speed Relationship
          type: array
          items:
            type: string
      required:
        - label
        - items
    UserRequest:
      type: object
      properties:
        grade:
          type: string
          description: Grade level of the student
          example: Primary 3
        schoolId:
          type: string
          description: School ID for school-specific examination formats
          example: school-123
        topic:
          type: string
          description: Main subject area (e.g., Mathematics, Science, English)
          example: Mathematics
        subject:
          type: string
          description: >-
            Specific lesson content or sub-topic (e.g., "Dividing a whole number
            by a proper fraction")
          example: Dividing a whole number by a proper fraction
        parentSubject:
          type: string
          description: Chapter or unit (e.g., "Fractions", "Algebra")
          example: Fractions
        subjectData:
          description: Structured subject data with labels and items
          type: array
          items:
            $ref: '#/components/schemas/SubjectDataItem'
        difficulty:
          type: string
          description: Difficulty level of the exercise
          example: Medium
          enum:
            - Easy
            - Medium
            - Hard
        totalQuestions:
          type: number
          description: Total number of questions in the exercise
          example: 10
          minimum: 1
        isCustomQuestionCount:
          type: boolean
          description: Indicates if a custom question count was used
          example: true
        exerciseType:
          description: Types of exercises to generate
          example:
            - Multiple Choice
            - Open-ended
          type: array
          items:
            type: array
        exerciseTypeDistribution:
          type: object
          description: Distribution of questions by exercise type
          example:
            Multiple Choice: 5
            Open-ended: 3
        includeImages:
          type: boolean
          description: Whether to include images in the questions
          example: false
          default: false
        selectedOptions:
          type: object
          description: Selected options from the worksheet
        worksheetId:
          type: string
          description: ID of the worksheet, if applicable
          example: worksheet-xyz-123
      required:
        - grade
        - topic
        - difficulty
        - totalQuestions
        - exerciseType
    GenPromptDto:
      type: object
      properties:
        relevantContent:
          description: Relevant content to use for generating the exercise
          example:
            - Content about fractions
            - Content about decimals
          type: array
          items:
            type: array
        userRequest:
          description: User request details for the exercise
          allOf:
            - $ref: '#/components/schemas/UserRequest'
        worksheetId:
          type: string
          description: ID of the worksheet for tracking progress
      required:
        - relevantContent
        - userRequest
    AiServiceLog:
      type: object
      properties: {}
    FileUpdateDto:
      type: object
      properties:
        description:
          type: string
          description: Description of the file
        category:
          type: string
          description: Category of the file
        tags:
          description: Tags for the file
          type: array
          items:
            type: string
    QuestionPoolResponseDto:
      type: object
      properties:
        id:
          type: string
          description: Question ID
        type:
          type: string
          description: Question type
          enum:
            - multiple_choice
            - true_false
            - short_answer
            - long_answer
            - fill_in_the_blank
            - matching
            - ordering
            - calculation
            - diagram
            - essay
        content:
          type: string
          description: Question content
        options:
          description: Answer options
          type: array
          items:
            type: string
        answer:
          description: Correct answers
          type: array
          items:
            type: string
        explain:
          type: string
          description: Explanation
        subject:
          type: string
          description: Subject
        parentSubject:
          type: string
          description: Parent subject
        childSubject:
          type: string
          description: Child subject
        grade:
          type: string
          description: Grade level
        difficulty:
          type: string
          description: Question difficulty
          enum:
            - very_easy
            - easy
            - medium
            - hard
            - very_hard
        status:
          type: string
          description: Question status
          enum:
            - active
            - inactive
            - draft
            - archived
            - under_review
        language:
          type: string
          description: Language
        image:
          type: string
          description: Image URL
        imagePrompt:
          type: string
          description: Image prompt
        tags:
          description: Tags
          type: array
          items:
            type: string
        optionValue:
          type: object
          description: Option value reference
        createdAt:
          format: date-time
          type: string
          description: Creation timestamp
        updatedAt:
          format: date-time
          type: string
          description: Last update timestamp
      required:
        - id
        - type
        - content
        - options
        - answer
        - explain
        - createdAt
        - updatedAt
    QuestionPoolSearchResultDto:
      type: object
      properties:
        questions:
          description: Array of questions
          type: array
          items:
            $ref: '#/components/schemas/QuestionPoolResponseDto'
        total:
          type: number
          description: Total number of questions matching the criteria
        page:
          type: number
          description: Current page number
        limit:
          type: number
          description: Number of items per page
        totalPages:
          type: number
          description: Total number of pages
        hasNextPage:
          type: boolean
          description: Whether there are more pages
        hasPreviousPage:
          type: boolean
          description: Whether there are previous pages
      required:
        - questions
        - total
        - page
        - limit
        - totalPages
        - hasNextPage
        - hasPreviousPage
    QuestionPoolFiltersDto:
      type: object
      properties:
        subjects:
          description: Available subjects
          example:
            - Mathematics
            - Science
            - English
          type: array
          items:
            type: string
        parentSubjects:
          description: Available parent subjects
          type: array
          items:
            type: string
        childSubjects:
          description: Available child subjects
          type: array
          items:
            type: string
        grades:
          description: Available grades
          example:
            - Primary 1
            - Primary 2
            - Secondary 1
          type: array
          items:
            type: string
        types:
          type: array
          description: Available question types
          items:
            type: string
            enum:
              - multiple_choice
              - true_false
              - short_answer
              - long_answer
              - fill_in_the_blank
              - matching
              - ordering
              - calculation
              - diagram
              - essay
        difficulties:
          type: array
          description: Available difficulty levels
          items:
            type: string
            enum:
              - very_easy
              - easy
              - medium
              - hard
              - very_hard
        languages:
          description: Available languages
          example:
            - English
            - Chinese
            - Malay
          type: array
          items:
            type: string
        tags:
          description: Available tags
          type: array
          items:
            type: string
        totalQuestions:
          type: number
          description: Total number of questions in the pool
      required:
        - subjects
        - parentSubjects
        - childSubjects
        - grades
        - types
        - difficulties
        - languages
        - tags
        - totalQuestions
    SubjectItemDto:
      type: object
      properties:
        label:
          type: string
          description: Label of the subject
          example: Speed
        items:
          description: Items within the subject
          example:
            - Speed Conversion
            - Time, Distance and Speed Relationship
          type: array
          items:
            type: string
      required:
        - label
        - items
    QuestionTypeDto:
      type: object
      properties:
        label:
          type: string
          description: Label of the question type
          example: Fill Blank
        count:
          type: number
          description: Count of questions for this type
          example: 3
      required:
        - label
        - count
    WorksheetOptionDto:
      type: object
      properties:
        key:
          type: string
          description: Unique key for the worksheet option
          example: difficulty
        value:
          type: string
          description: Value of the worksheet option
          example: easy
        text:
          type: string
          description: Display text for the worksheet option (optional)
          example: Easy difficulty
        count:
          type: number
          description: Number of questions for this option (used with exerciseType)
          example: 5
      required:
        - key
        - value
    CreateWorksheetDto:
      type: object
      properties:
        title:
          type: string
          description: Title of the worksheet
          example: Math Worksheet - Fractions
        description:
          type: string
          description: Description of the worksheet
          example: A worksheet for practicing fractions
        grade:
          type: string
          description: Grade level
          example: P6
        topic:
          type: string
          description: Topic of the worksheet
          example: Mathematics
        subject:
          description: Subject items
          type: array
          items:
            $ref: '#/components/schemas/SubjectItemDto'
        level:
          type: string
          description: Difficulty level
          example: Medium
        language:
          type: string
          description: Language of the worksheet
          example: English
        question_count:
          type: string
          description: Number of questions
          example: '10'
        isCustomQuestionCount:
          type: boolean
          description: Flag indicating if question_count is a custom value
          example: true
        question_type:
          description: Question types
          type: array
          items:
            $ref: '#/components/schemas/QuestionTypeDto'
        options:
          description: Legacy options for the worksheet (for backward compatibility)
          type: array
          items:
            $ref: '#/components/schemas/WorksheetOptionDto'
        questionSourceStrategy:
          type: string
          description: Strategy for question sourcing
          enum:
            - pool-only
            - ai-only
            - hybrid
            - mixed
          example: hybrid
    EQuestionType:
      type: string
      enum:
        - multiple_choice
        - true_false
        - short_answer
        - long_answer
        - fill_in_the_blank
        - matching
        - ordering
        - calculation
        - diagram
        - essay
      description: Question type
    QuestionMediaDto:
      type: object
      properties:
        imageUrl:
          type: string
          description: Image URL
        imagePrompt:
          type: string
          description: Image generation prompt
        audioUrl:
          type: string
          description: Audio URL
        videoUrl:
          type: string
          description: Video URL
        attachmentUrls:
          description: Attachment URLs
          type: array
          items:
            type: string
    QuestionMetadataDto:
      type: object
      properties:
        tags:
          description: Question tags
          type: array
          items:
            type: string
        keywords:
          description: Keywords for search
          type: array
          items:
            type: string
        estimatedTimeMinutes:
          type: number
          description: Estimated time in minutes
        cognitiveLevel:
          type: string
          description: Cognitive level (Bloom's taxonomy)
        learningObjectives:
          description: Learning objectives
          type: array
          items:
            type: string
        prerequisites:
          description: Prerequisites
          type: array
          items:
            type: string
        hints:
          description: Hints for students
          type: array
          items:
            type: string
        references:
          description: Reference materials
          type: array
          items:
            type: string
        authorNotes:
          type: string
          description: Author notes
        reviewNotes:
          type: string
          description: Review notes
        lastReviewDate:
          format: date-time
          type: string
          description: Last review date
        nextReviewDate:
          format: date-time
          type: string
          description: Next review date
    AddQuestionToWorksheetDto:
      type: object
      properties:
        type:
          description: Question type
          example: multiple_choice
          allOf:
            - $ref: '#/components/schemas/EQuestionType'
        content:
          type: string
          description: Question content/text
          example: What is the capital of France?
        options:
          description: Answer options
          example: &ref_30
            - Paris
            - London
            - Berlin
            - Madrid
          type: array
          items:
            type: string
        answer:
          description: Correct answer(s)
          example: &ref_31
            - Paris
          type: array
          items:
            type: string
        explain:
          type: string
          description: Explanation of the correct answer
          example: Paris is the capital and largest city of France.
        subject:
          type: string
          description: Subject
        parentSubject:
          type: string
          description: Parent subject
        childSubject:
          type: string
          description: Child subject
        topic:
          type: string
          description: Topic
        subtopic:
          type: string
          description: Subtopic
        grade:
          type: string
          description: Grade level
        difficulty:
          type: string
          description: Question difficulty
          enum: &ref_32
            - very_easy
            - easy
            - medium
            - hard
            - very_hard
        media:
          description: Media attachments
          allOf:
            - $ref: '#/components/schemas/QuestionMediaDto'
        imagePrompt:
          type: string
          description: Legacy image prompt
        imageUrl:
          type: string
          description: Legacy image URL
        status:
          type: string
          description: Question status
          enum: &ref_33
            - active
            - inactive
            - draft
            - archived
            - under_review
          default: active
        isPublic:
          type: boolean
          description: Is question public
        metadata:
          description: Question metadata
          allOf:
            - $ref: '#/components/schemas/QuestionMetadataDto'
        position:
          type: number
          description: Position in worksheet (if not provided, will be added at the end)
          minimum: 1
        points:
          type: number
          description: Points allocated for this question
          minimum: 0
          maximum: 100
          default: 1
        optionTypeId:
          type: string
          description: Database option type ID for categorizing the question context
          example: 123e4567-e89b-12d3-a456-************
        optionValueId:
          type: string
          description: >-
            Database option value ID for specific option selection within the
            type
          example: 987fcdeb-51a2-43d1-b789-123456789abc
        questionPoolId:
          type: string
          description: >-
            Question Pool ID - if provided, the question will be copied from the
            question pool instead of creating a new one
          example: 507f1f77bcf86cd799439011
      required:
        - type
        - content
        - options
        - answer
        - explain
    UpdateWorksheetQuestionDto:
      type: object
      properties:
        type:
          description: Question type
          example: multiple_choice
          allOf:
            - $ref: '#/components/schemas/EQuestionType'
        content:
          type: string
          description: Question content/text
          example: What is the capital of France?
        options:
          description: Answer options
          example: *ref_30
          type: array
          items:
            type: string
        answer:
          description: Correct answer(s)
          example: *ref_31
          type: array
          items:
            type: string
        explain:
          type: string
          description: Explanation of the correct answer
          example: Paris is the capital and largest city of France.
        subject:
          type: string
          description: Subject
        parentSubject:
          type: string
          description: Parent subject
        childSubject:
          type: string
          description: Child subject
        topic:
          type: string
          description: Topic
        subtopic:
          type: string
          description: Subtopic
        grade:
          type: string
          description: Grade level
        difficulty:
          type: string
          description: Question difficulty
          enum: *ref_32
        media:
          description: Media attachments
          allOf:
            - $ref: '#/components/schemas/QuestionMediaDto'
        imagePrompt:
          type: string
          description: Legacy image prompt
        imageUrl:
          type: string
          description: Legacy image URL
        status:
          type: string
          description: Question status
          enum: *ref_33
          default: active
        isPublic:
          type: boolean
          description: Is question public
        metadata:
          description: Question metadata
          allOf:
            - $ref: '#/components/schemas/QuestionMetadataDto'
        position:
          type: number
          description: Position in worksheet (if not provided, will be added at the end)
          minimum: 1
        points:
          type: number
          description: Points allocated for this question
          minimum: 0
          maximum: 100
          default: 1
        optionTypeId:
          type: string
          description: Database option type ID for categorizing the question context
          example: 123e4567-e89b-12d3-a456-************
        optionValueId:
          type: string
          description: >-
            Database option value ID for specific option selection within the
            type
          example: 987fcdeb-51a2-43d1-b789-123456789abc
        questionPoolId:
          type: string
          description: >-
            Question Pool ID - if provided, the question will be copied from the
            question pool instead of creating a new one
          example: 507f1f77bcf86cd799439011
        version:
          type: number
          description: Version number for optimistic locking
          minimum: 1
        updateReason:
          type: string
          description: Reason for the update (for audit purposes)
    ReplaceWorksheetQuestionDto:
      type: object
      properties:
        type:
          description: Question type
          example: multiple_choice
          allOf:
            - $ref: '#/components/schemas/EQuestionType'
        content:
          type: string
          description: Question content/text
          example: What is the capital of France?
        options:
          description: Answer options
          example: *ref_30
          type: array
          items:
            type: string
        answer:
          description: Correct answer(s)
          example: *ref_31
          type: array
          items:
            type: string
        explain:
          type: string
          description: Explanation of the correct answer
          example: Paris is the capital and largest city of France.
        subject:
          type: string
          description: Subject
        parentSubject:
          type: string
          description: Parent subject
        childSubject:
          type: string
          description: Child subject
        topic:
          type: string
          description: Topic
        subtopic:
          type: string
          description: Subtopic
        grade:
          type: string
          description: Grade level
        difficulty:
          type: string
          description: Question difficulty
          enum: *ref_32
        media:
          description: Media attachments
          allOf:
            - $ref: '#/components/schemas/QuestionMediaDto'
        imagePrompt:
          type: string
          description: Legacy image prompt
        imageUrl:
          type: string
          description: Legacy image URL
        status:
          type: string
          description: Question status
          enum: *ref_33
          default: active
        isPublic:
          type: boolean
          description: Is question public
        metadata:
          description: Question metadata
          allOf:
            - $ref: '#/components/schemas/QuestionMetadataDto'
        position:
          type: number
          description: Position in worksheet (if not provided, will be added at the end)
          minimum: 1
        points:
          type: number
          description: Points allocated for this question
          minimum: 0
          maximum: 100
          default: 1
        optionTypeId:
          type: string
          description: Database option type ID for categorizing the question context
          example: 123e4567-e89b-12d3-a456-************
        optionValueId:
          type: string
          description: >-
            Database option value ID for specific option selection within the
            type
          example: 987fcdeb-51a2-43d1-b789-123456789abc
        questionPoolId:
          type: string
          description: >-
            Question Pool ID - if provided, the question will be copied from the
            question pool instead of creating a new one
          example: 507f1f77bcf86cd799439011
        version:
          type: number
          description: Version number for optimistic locking
          minimum: 1
        updateReason:
          type: string
          description: Reason for the replacement (for audit purposes)
      required:
        - type
        - content
        - options
        - answer
        - explain
        - version
    ReorderQuestionDto:
      type: object
      properties:
        questionId:
          type: string
          description: Question ID to reorder
        newPosition:
          type: number
          description: New position for the question
          minimum: 1
      required:
        - questionId
        - newPosition
    BulkReorderQuestionsDto:
      type: object
      properties:
        reorders:
          description: Array of question reorder operations
          type: array
          items:
            $ref: '#/components/schemas/ReorderQuestionDto'
      required:
        - reorders
    BulkAddQuestionsDto:
      type: object
      properties:
        questions:
          description: Array of questions to add to the worksheet
          minItems: 1
          maxItems: 50
          type: array
          items:
            $ref: '#/components/schemas/AddQuestionToWorksheetDto'
        insertPosition:
          type: number
          description: >-
            Position to insert questions (if not provided, will be added at the
            end)
          minimum: 1
        validateQuestions:
          type: boolean
          description: Whether to validate questions before adding
          default: true
        reason:
          type: string
          description: Reason for bulk addition (for audit purposes)
      required:
        - questions
    BulkOperationResponseDto:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the operation was successful
        successCount:
          type: number
          description: Number of items processed successfully
        failureCount:
          type: number
          description: Number of items that failed
        totalCount:
          type: number
          description: Total number of items processed
        successes:
          description: Array of successful operations
          type: array
          items:
            type: string
        failures:
          description: Array of failed operations with error details
          type: array
          items:
            type: string
        timestamp:
          type: string
          description: Operation timestamp
        processingTimeMs:
          type: number
          description: Processing time in milliseconds
      required:
        - success
        - successCount
        - failureCount
        - totalCount
        - timestamp
        - processingTimeMs
    BulkRemoveQuestionsDto:
      type: object
      properties:
        questionIds:
          description: Question IDs to remove from the worksheet
          minItems: 1
          maxItems: 50
          type: array
          items:
            type: string
        reason:
          type: string
          description: Reason for bulk removal (for audit purposes)
        forceRemoval:
          type: boolean
          description: >-
            Whether to force removal even if it would leave the worksheet with
            no questions
          default: false
      required:
        - questionIds
    BulkUpdateQuestionsDto:
      type: object
      properties:
        updates:
          description: Array of question updates
          minItems: 1
          maxItems: 50
          type: array
          items:
            type: string
        reason:
          type: string
          description: Reason for bulk update (for audit purposes)
        validateQuestions:
          type: boolean
          description: Whether to validate questions after updating
          default: true
      required:
        - updates
    PoolUtilizationResponseDto:
      type: object
      properties:
        timeframe:
          type: string
          description: Timeframe for the metrics
        timestamp:
          format: date-time
          type: string
          description: Timestamp when metrics were calculated
        totalUniqueQuestionsInPool:
          type: number
          description: Total unique questions in the pool
        uniqueQuestionsUsed:
          type: number
          description: Number of unique questions used
        utilizationRate:
          type: number
          description: Overall utilization rate (0-1)
        subjectBreakdown:
          type: object
          description: Breakdown by subject
          additionalProperties: true
      required:
        - timeframe
        - timestamp
        - totalUniqueQuestionsInPool
        - uniqueQuestionsUsed
        - utilizationRate
        - subjectBreakdown
    MostReusedQuestionDto:
      type: object
      properties:
        questionId:
          type: string
          description: Question ID
        usageCount:
          type: number
          description: Number of times this question was used
        questionType:
          type: string
          description: Type of the question
        subject:
          type: string
          description: Subject of the question
      required:
        - questionId
        - usageCount
        - questionType
        - subject
    QuestionReuseResponseDto:
      type: object
      properties:
        timeframe:
          type: string
          description: Timeframe for the metrics
        timestamp:
          format: date-time
          type: string
          description: Timestamp when metrics were calculated
        averageReuseFrequency:
          type: number
          description: Average reuse frequency across all questions
        mostReusedQuestions:
          description: Top 10 most reused questions
          type: array
          items:
            $ref: '#/components/schemas/MostReusedQuestionDto'
        reuseDistribution:
          type: object
          description: Distribution of reuse counts
          additionalProperties:
            type: number
      required:
        - timeframe
        - timestamp
        - averageReuseFrequency
        - mostReusedQuestions
        - reuseDistribution
    TimeStatsDto:
      type: object
      properties:
        average:
          type: number
          description: Average time in milliseconds
        p50:
          type: number
          description: 50th percentile time in milliseconds
        p95:
          type: number
          description: 95th percentile time in milliseconds
        p99:
          type: number
          description: 99th percentile time in milliseconds
      required:
        - average
        - p50
        - p95
        - p99
    GenerationTimeResponseDto:
      type: object
      properties:
        timeframe:
          type: string
          description: Timeframe for the metrics
        timestamp:
          format: date-time
          type: string
          description: Timestamp when metrics were calculated
        poolSelectionTime:
          description: Pool selection time statistics
          allOf:
            - $ref: '#/components/schemas/TimeStatsDto'
        aiGenerationTime:
          description: AI generation time statistics
          allOf:
            - $ref: '#/components/schemas/TimeStatsDto'
        comparisonRatio:
          type: number
          description: Ratio of pool time to AI time
      required:
        - timeframe
        - timestamp
        - poolSelectionTime
        - aiGenerationTime
        - comparisonRatio
    ValidationResponseDto:
      type: object
      properties:
        timeframe:
          type: string
          description: Timeframe for the metrics
        timestamp:
          format: date-time
          type: string
          description: Timestamp when metrics were calculated
        totalValidations:
          type: number
          description: Total number of validations performed
        successfulValidations:
          type: number
          description: Number of successful validations
        successRate:
          type: number
          description: Success rate (0-1)
        issueBreakdown:
          type: object
          description: Breakdown of issues by type
          additionalProperties: true
      required:
        - timeframe
        - timestamp
        - totalValidations
        - successfulValidations
        - successRate
        - issueBreakdown
    DashboardMetricsResponseDto:
      type: object
      properties:
        poolUtilization:
          description: Pool utilization metrics
          allOf:
            - $ref: '#/components/schemas/PoolUtilizationResponseDto'
        questionReuse:
          description: Question reuse metrics
          allOf:
            - $ref: '#/components/schemas/QuestionReuseResponseDto'
        generationTimeComparison:
          description: Generation time comparison metrics
          allOf:
            - $ref: '#/components/schemas/GenerationTimeResponseDto'
        validationMetrics:
          description: Validation success metrics
          allOf:
            - $ref: '#/components/schemas/ValidationResponseDto'
        cacheMetrics:
          type: object
          description: Cache performance metrics
          additionalProperties: true
        generatedAt:
          format: date-time
          type: string
          description: When the metrics were generated
        timeframe:
          type: string
          description: Timeframe used for aggregation
        dateRange:
          type: object
          description: Date range for the metrics
          properties:
            startDate:
              type: string
              format: date-time
            endDate:
              type: string
              format: date-time
      required:
        - poolUtilization
        - questionReuse
        - generationTimeComparison
        - validationMetrics
        - cacheMetrics
        - generatedAt
        - timeframe
        - dateRange
    CacheResponseDto:
      type: object
      properties:
        timeframe:
          type: string
          description: Timeframe for the metrics
        timestamp:
          format: date-time
          type: string
          description: Timestamp when metrics were calculated
        cacheType:
          type: string
          description: Cache type
        totalRequests:
          type: number
          description: Total number of cache requests
        hits:
          type: number
          description: Number of cache hits
        misses:
          type: number
          description: Number of cache misses
        hitRate:
          type: number
          description: Cache hit rate (0-1)
        averageResponseTime:
          type: number
          description: Average response time in milliseconds
      required:
        - timeframe
        - timestamp
        - cacheType
        - totalRequests
        - hits
        - misses
        - hitRate
        - averageResponseTime
    CreateOptionTypeDto:
      type: object
      properties:
        key:
          type: string
          description: Unique key for the option type
          example: difficulty_level
        label:
          type: string
          description: Display label for the option type
          example: Difficulty Level
        description:
          type: string
          description: Description of the option type
          example: Difficulty levels for exercises
        order:
          type: number
          description: Display order for the option type (optional)
          example: 1
      required:
        - key
        - label
        - description
    CreateOptionValueDto:
      type: object
      properties:
        name:
          type: string
          description: Name of the option value
          example: Easy
        optionTypeId:
          type: string
          description: UUID of the parent option type
          example: 123e4567-e89b-12d3-a456-************
        order:
          type: number
          description: Display order for the option value (optional)
          example: 1
        isActive:
          type: boolean
          description: Whether the option value is active (optional, defaults to true)
          example: true
          default: true
      required:
        - name
        - optionTypeId
    CreateSubjectDto:
      type: object
      properties:
        name:
          type: string
          description: Name of the subject
          example: Mathematics
        description:
          type: string
          description: Description of the subject
          example: Mathematics for primary school students
        type:
          type: string
          description: Type of subject (parent or child)
          enum:
            - parent
            - child
          default: child
        parentId:
          type: string
          description: ID of the parent subject
          example: 123e4567-e89b-12d3-a456-************
        topicIds:
          description: IDs of associated topics
          example:
            - 123e4567-e89b-12d3-a456-************
          type: array
          items:
            type: array
        gradeIds:
          description: IDs of associated grades
          example:
            - 123e4567-e89b-12d3-a456-************
          type: array
          items:
            type: array
      required:
        - name
    CreateExamDto:
      type: object
      properties: {}
    SubmitExamDto:
      type: object
      properties: {}
