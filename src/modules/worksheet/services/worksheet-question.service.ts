import { 
  Injectable, 
  NotFoundException, 
  BadRequestException, 
  ForbiddenException,
  ConflictException,
  Logger 
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectModel } from '@nestjs/mongoose';
import { Repository } from 'typeorm';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { CreateExerciseQuestionDto } from '../dto/exercise-question.dto';
import { AddQuestionToWorksheetDto, UpdateWorksheetQuestionDto, ReplaceWorksheetQuestionDto, BulkReorderQuestionsDto, ReorderQuestionDto } from '../dto/worksheet-question.dto';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';
import { EUserRole } from '../../user/dto/create-user.dto';
import { WorksheetQuestionAuditService } from './worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { QuestionPoolService } from '../../question-pool/question-pool.service';
import { QuestionPool } from '../../mongodb/schemas/question-pool.schema';
import { WorksheetQuestionCollaborationGateway } from '../gateways/worksheet-question-collaboration.gateway';
import { WorksheetQuestionLockingService } from './worksheet-question-locking.service';
import { CollaborationEvent } from '../enums/collaboration-events.enum';
import { MonitorDbPerformance, MonitorConnectionPool } from '../decorators/db-performance.decorator';
import { WorksheetQuestionMetricsService } from './worksheet-question-metrics.service';
import { WorksheetQuestionEnhancedCacheService } from './worksheet-question-enhanced-cache.service';

export interface UserContext {
  sub: string;
  email: string;
  role: EUserRole;
  schoolId?: string | null;
}

@Injectable()
export class WorksheetQuestionService {
  private readonly logger = new Logger(WorksheetQuestionService.name);

  constructor(
    @InjectRepository(Worksheet)
    private readonly worksheetRepository: Repository<Worksheet>,
    @InjectModel(WorksheetQuestionDocument.name)
    private readonly worksheetQuestionModel: Model<WorksheetQuestionDocument>,
    private readonly auditService: WorksheetQuestionAuditService,
    private readonly socketGateway: SocketGateway,
    private readonly collaborationGateway: WorksheetQuestionCollaborationGateway,
    private readonly lockingService: WorksheetQuestionLockingService,
    private readonly worksheetQuestionMetricsService: WorksheetQuestionMetricsService,
    private readonly enhancedCacheService: WorksheetQuestionEnhancedCacheService,
    private readonly questionPoolService: QuestionPoolService,
  ) {}

  /**
   * Add a new question to a worksheet
   */
  async addQuestionToWorksheet(
    worksheetId: string,
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    this.logger.log(`Adding question to worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Check question limit
    await this.validateQuestionLimit(worksheet);

    // Step 3: Create the question
    const newQuestion = await this.createQuestion(questionDto, user, worksheet);

    // Step 4: Update worksheet in database
    await this.updateWorksheetWithNewQuestion(worksheet, newQuestion, user);

    // Step 5: Update MongoDB cache
    await this.updateQuestionCache(worksheetId, worksheet.questions, user);

    // Step 6: Emit real-time updates (both legacy and collaboration)
    await this.emitQuestionUpdate(worksheetId, 'question_added', {
      question: newQuestion,
      totalQuestions: worksheet.questions.length,
      worksheetId
    }, user.sub);

    // Step 6b: Emit collaboration event
    await this.collaborationGateway.broadcastQuestionUpdate(
      worksheetId,
      CollaborationEvent.QUESTION_ADDED_REALTIME,
      {
        question: newQuestion,
        totalQuestions: worksheet.questions.length,
        addedBy: user.sub
      },
      user.sub
    );

    // Step 7: Log audit event
    await this.auditService.logQuestionAdded(worksheetId, newQuestion.id!, user);

    this.logger.log(`Successfully added question ${newQuestion.id} to worksheet ${worksheetId}`);
    return newQuestion;
  }

  /**
   * Validate user access to worksheet and return worksheet
   */
  @MonitorDbPerformance('findWorksheet', 'worksheets')
  @MonitorConnectionPool()
  private async validateWorksheetAccess(
    worksheetId: string,
    user: UserContext
  ): Promise<Worksheet> {
    const worksheet = await this.worksheetRepository.findOne({
      where: { id: worksheetId },
      relations: ['selectedOptions']
    });

    if (!worksheet) {
      throw new NotFoundException(`Worksheet with ID ${worksheetId} not found`);
    }

    // Admin has access to all worksheets
    if (user.role === EUserRole.ADMIN) {
      return worksheet;
    }

    // School-based access control
    if (user.role === EUserRole.SCHOOL_MANAGER) {
      if (!user.schoolId) {
        throw new ForbiddenException('School manager must be assigned to a school');
      }
      if (worksheet.schoolId !== user.schoolId) {
        throw new ForbiddenException('Cannot modify questions from different school');
      }
      return worksheet;
    }

    // Independent teacher can only modify their own worksheets
    if (user.role === EUserRole.INDEPENDENT_TEACHER) {
      if (worksheet.createdBy !== user.sub) {
        throw new ForbiddenException('Can only modify your own worksheets');
      }
      return worksheet;
    }

    // Regular teacher can modify worksheets in their school
    if (user.role === EUserRole.TEACHER) {
      if (!user.schoolId || worksheet.schoolId !== user.schoolId) {
        throw new ForbiddenException('Access denied to worksheet from different school');
      }
      return worksheet;
    }

    throw new ForbiddenException('Insufficient permissions to modify worksheet questions');
  }

  /**
   * Validate that adding a question won't exceed the limit
   */
  private async validateQuestionLimit(worksheet: Worksheet): Promise<void> {
    const currentQuestionCount = worksheet.questions?.length || 0;
    const maxQuestions = worksheet.maxQuestions || 100;

    if (currentQuestionCount >= maxQuestions) {
      throw new BadRequestException(
        `Question limit exceeded. Current: ${currentQuestionCount}, Maximum: ${maxQuestions}`
      );
    }
  }

  /**
   * Create a new question from DTO
   */
  private async createQuestion(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet
  ): Promise<IExerciseQuestion> {
    const questionId = uuidv4();
    const currentQuestions = worksheet.questions || [];
    const position = questionDto.position || currentQuestions.length + 1;

    // If questionPoolId is provided, get the question from the pool
    if (questionDto.questionPoolId) {
      return this.createQuestionFromPool(questionDto, user, worksheet, questionId, position);
    }

    // Otherwise, create a new question from the provided data
    return this.createQuestionFromDto(questionDto, user, worksheet, questionId, position);
  }

  /**
   * Create a question from the question pool
   */
  private async createQuestionFromPool(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet,
    questionId: string,
    position: number
  ): Promise<IExerciseQuestion> {
    // Get the question from the pool
    const poolQuestion = await this.questionPoolService.getQuestionById(questionDto.questionPoolId!);

    if (!poolQuestion) {
      throw new NotFoundException(`Question with ID ${questionDto.questionPoolId} not found in question pool`);
    }

    // Validate user access to the pool question
    this.validatePoolQuestionAccess(poolQuestion, user);

    // Create the worksheet question based on the pool question
    const newQuestion: IExerciseQuestion = {
      id: questionId,
      position,
      points: questionDto.points || 1,

      // Copy question content from pool
      type: poolQuestion.type as any,
      content: poolQuestion.content,
      options: poolQuestion.options || [],
      answer: poolQuestion.answer || [],
      explain: poolQuestion.explain || '',

      // Copy subject and academic information
      subject: poolQuestion.subject,
      parentSubject: poolQuestion.parentSubject,
      childSubject: poolQuestion.childSubject,
      grade: poolQuestion.grade,
      difficulty: poolQuestion.difficultyLevel as any,

      // Copy media information
      image: poolQuestion.image,
      imagePrompt: poolQuestion.imagePrompt,

      // Override with any provided values from DTO
      ...(questionDto.optionTypeId && { optionTypeId: questionDto.optionTypeId }),
      ...(questionDto.optionValueId && { optionValueId: questionDto.optionValueId }),

      // Add pool reference for tracking
      questionPoolId: questionDto.questionPoolId,
      sourceType: 'question_pool',

      // Audit fields
      audit: {
        createdBy: user.sub,
        createdAt: new Date(),
        updatedBy: user.sub,
        updatedAt: new Date(),
        version: 1
      },

      // School association
      schoolId: user.schoolId || undefined
    };

    // Update pool question usage statistics
    await this.updatePoolQuestionUsage(questionDto.questionPoolId!);

    return newQuestion;
  }

  /**
   * Create a question from DTO data (original functionality)
   */
  private createQuestionFromDto(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet,
    questionId: string,
    position: number
  ): IExerciseQuestion {

    const newQuestion: IExerciseQuestion = {
      id: questionId,
      position,
      points: questionDto.points || 1,
      type: questionDto.type,
      content: questionDto.content,
      options: questionDto.options,
      answer: questionDto.answer,
      explain: questionDto.explain,
      subject: questionDto.subject,
      parentSubject: questionDto.parentSubject,
      childSubject: questionDto.childSubject,
      topic: questionDto.topic,
      subtopic: questionDto.subtopic,
      grade: questionDto.grade,
      difficulty: questionDto.difficulty,
      media: questionDto.media,
      imagePrompt: questionDto.imagePrompt,
      imageUrl: questionDto.imageUrl,
      status: questionDto.status, // Use the status from the base DTO
      isPublic: questionDto.isPublic,
      metadata: questionDto.metadata,
      // Database option references (if provided)
      optionTypeId: questionDto.optionTypeId,
      optionValueId: questionDto.optionValueId,
      // Audit fields
      audit: {
        createdBy: user.sub,
        createdAt: new Date(),
        updatedBy: user.sub,
        updatedAt: new Date(),
        version: 1
      },
      // School association
      schoolId: user.schoolId || undefined
    };

    return newQuestion;
  }

  /**
   * Update worksheet with new question
   */
  private async updateWorksheetWithNewQuestion(
    worksheet: Worksheet,
    newQuestion: IExerciseQuestion,
    user: UserContext
  ): Promise<void> {
    if (!worksheet.questions) {
      worksheet.questions = [];
    }

    worksheet.questions.push(newQuestion);
    worksheet.totalQuestions = worksheet.questions.length;
    worksheet.lastModifiedBy = user.sub;

    await this.worksheetRepository.save(worksheet);
  }

  /**
   * Update MongoDB question cache and Redis cache
   */
  @MonitorDbPerformance('updateCache', 'worksheet_questions')
  private async updateQuestionCache(
    worksheetId: string,
    questions: IExerciseQuestion[],
    user: UserContext
  ): Promise<void> {
    try {
      // Update MongoDB cache
      await this.worksheetQuestionModel.findOneAndUpdate(
        { worksheetId },
        {
          $set: {
            questions,
            totalQuestions: questions.length,
            lastModifiedBy: user.sub,
            lastModifiedAt: new Date(),
            schoolId: user.schoolId || undefined
          },
          $inc: { version: 1 }
        },
        { upsert: true, new: true }
      );

      // Update Redis cache
      await this.enhancedCacheService.cacheWorksheetQuestions(
        worksheetId,
        questions,
        user.schoolId || undefined
      );

      this.logger.debug(`Updated both MongoDB and Redis cache for worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Failed to update question cache for worksheet ${worksheetId}`, error);
      // Don't throw error - cache update failure shouldn't fail the operation
    }
  }

  /**
   * Remove a question from a worksheet
   */
  async removeQuestionFromWorksheet(
    worksheetId: string,
    questionId: string,
    user: UserContext
  ): Promise<void> {
    this.logger.log(`Removing question ${questionId} from worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Find and validate the question exists in the worksheet
    const questionToRemove = await this.findQuestionInWorksheet(worksheet, questionId);

    // Step 3: Validate minimum questions requirement
    await this.validateMinimumQuestions(worksheet);

    // Step 4: Remove question from worksheet and reorder remaining questions
    await this.removeQuestionAndReorder(worksheet, questionId, user);

    // Step 5: Update MongoDB cache and invalidate Redis cache
    await this.updateQuestionCache(worksheetId, worksheet.questions, user);
    await this.enhancedCacheService.invalidateWorksheetCache(worksheetId);

    // Step 6: Emit real-time update
    await this.emitQuestionUpdate(worksheetId, 'question_removed', {
      questionId,
      totalQuestions: worksheet.questions.length,
      worksheetId
    }, user.sub);

    // Step 7: Log audit event
    await this.auditService.logQuestionRemoved(worksheetId, questionId, questionToRemove, user);

    this.logger.log(`Successfully removed question ${questionId} from worksheet ${worksheetId}`);
  }

  /**
   * Find a question in the worksheet and return it
   */
  private async findQuestionInWorksheet(
    worksheet: Worksheet,
    questionId: string
  ): Promise<IExerciseQuestion> {
    const questions = worksheet.questions || [];
    const question = questions.find(q => q.id === questionId);

    if (!question) {
      throw new NotFoundException(
        `Question with ID ${questionId} not found in worksheet ${worksheet.id}`
      );
    }

    return question;
  }

  /**
   * Validate that removing a question won't violate minimum requirements
   */
  private async validateMinimumQuestions(worksheet: Worksheet): Promise<void> {
    const currentQuestionCount = worksheet.questions?.length || 0;
    const minQuestions = 1; // Minimum 1 question per worksheet

    if (currentQuestionCount <= minQuestions) {
      throw new BadRequestException(
        `Cannot remove question. Worksheet must have at least ${minQuestions} question(s). Current count: ${currentQuestionCount}`
      );
    }
  }

  /**
   * Remove question from worksheet and reorder remaining questions
   */
  private async removeQuestionAndReorder(
    worksheet: Worksheet,
    questionId: string,
    user: UserContext
  ): Promise<void> {
    if (!worksheet.questions) {
      throw new NotFoundException('No questions found in worksheet');
    }

    // Remove the question
    const originalLength = worksheet.questions.length;
    worksheet.questions = worksheet.questions.filter(q => q.id !== questionId);

    if (worksheet.questions.length === originalLength) {
      throw new NotFoundException(`Question with ID ${questionId} not found in worksheet`);
    }

    // Reorder remaining questions to maintain sequential order
    worksheet.questions.forEach((question, index) => {
      question.order = index + 1;
      // Update audit info
      if (question.audit) {
        question.audit.updatedBy = user.sub;
        question.audit.updatedAt = new Date();
        question.audit.version = (question.audit.version || 1) + 1;
      }
    });

    // Update worksheet metadata
    worksheet.totalQuestions = worksheet.questions.length;
    worksheet.lastModifiedBy = user.sub;

    // Save the updated worksheet
    await this.worksheetRepository.save(worksheet);
  }

  /**
   * Update a question in a worksheet (PATCH - partial update)
   */
  async updateQuestionInWorksheet(
    worksheetId: string,
    questionId: string,
    updateDto: UpdateWorksheetQuestionDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    this.logger.log(`Updating question ${questionId} in worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Check if user can edit this question (has lock)
    const canEdit = await this.lockingService.canEditQuestion(worksheetId, questionId, user.sub);
    if (!canEdit) {
      throw new ConflictException('Question is locked by another user. Acquire lock before editing.');
    }

    // Step 2: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 3: Find and validate the question
    const questionIndex = await this.findQuestionIndex(worksheet, questionId);
    const existingQuestion = worksheet.questions[questionIndex];

    // Step 4: Validate optimistic locking if version is provided
    if (updateDto.version !== undefined) {
      await this.validateQuestionVersion(existingQuestion, updateDto.version);
    }

    // Step 5: Validate the update data
    await this.validateQuestionUpdate(updateDto, existingQuestion);

    // Step 6: Apply partial updates to the question
    const updatedQuestion = await this.applyQuestionUpdate(existingQuestion, updateDto, user);

    // Step 7: Update the question in the worksheet
    worksheet.questions[questionIndex] = updatedQuestion;
    worksheet.lastModifiedBy = user.sub;

    // Step 8: Save the updated worksheet
    await this.worksheetRepository.save(worksheet);

    // Step 9: Update MongoDB cache
    await this.updateQuestionCache(worksheetId, worksheet.questions, user);

    // Step 10: Emit real-time updates (both legacy and collaboration)
    await this.emitQuestionUpdate(worksheetId, 'question_updated', {
      question: updatedQuestion,
      questionId,
      worksheetId,
      updateType: 'partial'
    }, user.sub);

    // Step 10b: Emit collaboration event
    await this.collaborationGateway.broadcastQuestionUpdate(
      worksheetId,
      CollaborationEvent.QUESTION_UPDATED_REALTIME,
      {
        question: updatedQuestion,
        questionId,
        updateType: 'partial',
        updatedBy: user.sub
      },
      user.sub
    );

    // Step 11: Log audit event
    await this.auditService.logQuestionUpdatedInWorksheet(worksheetId, questionId, user, updateDto);

    this.logger.log(`Successfully updated question ${questionId} in worksheet ${worksheetId}`);
    return updatedQuestion;
  }

  /**
   * Replace a question in a worksheet (PUT - full replacement)
   */
  async replaceQuestionInWorksheet(
    worksheetId: string,
    questionId: string,
    replaceDto: ReplaceWorksheetQuestionDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    this.logger.log(`Replacing question ${questionId} in worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Find and validate the question
    const questionIndex = await this.findQuestionIndex(worksheet, questionId);
    const existingQuestion = worksheet.questions[questionIndex];

    // Step 3: Validate optimistic locking
    await this.validateQuestionVersion(existingQuestion, replaceDto.version);

    // Step 4: Validate the replacement data
    await this.validateQuestionReplacement(replaceDto);

    // Step 5: Create the replacement question
    const replacementQuestion = await this.createReplacementQuestion(
      existingQuestion,
      replaceDto,
      user
    );

    // Step 6: Replace the question in the worksheet
    worksheet.questions[questionIndex] = replacementQuestion;
    worksheet.lastModifiedBy = user.sub;

    // Step 7: Save the updated worksheet
    await this.worksheetRepository.save(worksheet);

    // Step 8: Update MongoDB cache
    await this.updateQuestionCache(worksheetId, worksheet.questions, user);

    // Step 9: Emit real-time update
    await this.emitQuestionUpdate(worksheetId, 'question_updated', {
      question: replacementQuestion,
      questionId,
      worksheetId,
      updateType: 'full'
    }, user.sub);

    // Step 10: Log audit event
    await this.auditService.logQuestionReplaced(worksheetId, questionId, user, replaceDto);

    this.logger.log(`Successfully replaced question ${questionId} in worksheet ${worksheetId}`);
    return replacementQuestion;
  }

  /**
   * Reorder questions in a worksheet (bulk operation)
   */
  async reorderQuestionsInWorksheet(
    worksheetId: string,
    reorderDto: BulkReorderQuestionsDto,
    user: UserContext
  ): Promise<{
    worksheetId: string;
    totalQuestions: number;
    reorderedQuestions: Array<{
      questionId: string;
      oldPosition: number;
      newPosition: number;
    }>;
    version: number;
  }> {
    this.logger.log(`Reordering ${reorderDto.reorders.length} questions in worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Validate optimistic locking
    await this.validateWorksheetVersion(worksheet, user);

    // Step 3: Validate reorder operations
    await this.validateReorderOperations(worksheet, reorderDto.reorders);

    // Step 4: Perform the reordering
    const reorderResults = await this.performQuestionReordering(worksheet, reorderDto.reorders, user);

    // Step 5: Save the updated worksheet
    await this.worksheetRepository.save(worksheet);

    // Step 6: Update MongoDB cache
    await this.updateQuestionCache(worksheetId, worksheet.questions, user);

    // Step 7: Emit real-time updates
    await this.emitQuestionUpdate(worksheetId, 'questions_reordered', {
      worksheetId,
      totalQuestions: worksheet.questions.length,
      reorderedQuestions: reorderResults,
      version: worksheet.questionMetadata?.questionVersion || 1
    }, user.sub);

    // Step 8: Log audit events
    for (const result of reorderResults) {
      await this.auditService.logQuestionMoved(
        result.questionId,
        result.oldPosition,
        result.newPosition,
        worksheetId,
        worksheetId,
        user.sub,
        { reorderOperation: true }
      );
    }

    this.logger.log(`Successfully reordered ${reorderResults.length} questions in worksheet ${worksheetId}`);

    return {
      worksheetId,
      totalQuestions: worksheet.questions.length,
      reorderedQuestions: reorderResults,
      version: worksheet.questionMetadata?.questionVersion || 1
    };
  }

  /**
   * Find question index in worksheet
   */
  private async findQuestionIndex(worksheet: Worksheet, questionId: string): Promise<number> {
    if (!worksheet.questions || worksheet.questions.length === 0) {
      throw new NotFoundException('No questions found in worksheet');
    }

    const questionIndex = worksheet.questions.findIndex(q => q.id === questionId);
    if (questionIndex === -1) {
      throw new NotFoundException(`Question with ID ${questionId} not found in worksheet`);
    }

    return questionIndex;
  }

  /**
   * Validate question version for optimistic locking
   */
  private async validateQuestionVersion(
    existingQuestion: IExerciseQuestion,
    providedVersion: number
  ): Promise<void> {
    const currentVersion = existingQuestion.audit?.version || 1;

    if (currentVersion !== providedVersion) {
      throw new ConflictException(
        `Question has been modified by another user. Expected version: ${providedVersion}, Current version: ${currentVersion}. Please refresh and try again.`
      );
    }
  }

  /**
   * Validate question update data
   */
  private async validateQuestionUpdate(
    updateDto: UpdateWorksheetQuestionDto,
    existingQuestion: IExerciseQuestion
  ): Promise<void> {
    // Validate answer format based on question type
    if (updateDto.type && updateDto.answer) {
      await this.validateAnswerFormat(updateDto.type, updateDto.answer);
    } else if (updateDto.answer && existingQuestion.type) {
      await this.validateAnswerFormat(existingQuestion.type, updateDto.answer);
    }

    // Validate options if provided
    if (updateDto.options) {
      await this.validateQuestionOptions(updateDto.options, updateDto.type || existingQuestion.type);
    }

    // Validate image prompt if provided
    if (updateDto.imagePrompt) {
      await this.validateImagePrompt(updateDto.imagePrompt);
    }

    // Validate educational standards compliance
    await this.validateEducationalStandards(updateDto);
  }

  /**
   * Validate question replacement data
   */
  private async validateQuestionReplacement(replaceDto: ReplaceWorksheetQuestionDto): Promise<void> {
    // Validate answer format
    await this.validateAnswerFormat(replaceDto.type, replaceDto.answer);

    // Validate options
    await this.validateQuestionOptions(replaceDto.options, replaceDto.type);

    // Validate image prompt if provided
    if (replaceDto.imagePrompt) {
      await this.validateImagePrompt(replaceDto.imagePrompt);
    }

    // Validate educational standards compliance
    await this.validateEducationalStandards(replaceDto);
  }

  /**
   * Apply partial updates to existing question
   */
  private async applyQuestionUpdate(
    existingQuestion: IExerciseQuestion,
    updateDto: UpdateWorksheetQuestionDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    const updatedQuestion: IExerciseQuestion = {
      ...existingQuestion,
      ...updateDto,
      id: existingQuestion.id, // Preserve ID
      audit: {
        createdAt: existingQuestion.audit?.createdAt || new Date(),
        updatedAt: new Date(),
        createdBy: existingQuestion.audit?.createdBy || user.sub,
        updatedBy: user.sub,
        version: (existingQuestion.audit?.version || 1) + 1,
        changeLog: [
          ...(existingQuestion.audit?.changeLog || []),
          {
            timestamp: new Date(),
            userId: user.sub,
            action: 'partial_update',
            changes: updateDto,
            reason: updateDto.updateReason
          }
        ]
      },
      schoolId: user.schoolId || undefined
    };

    return updatedQuestion;
  }

  /**
   * Create replacement question
   */
  private async createReplacementQuestion(
    existingQuestion: IExerciseQuestion,
    replaceDto: ReplaceWorksheetQuestionDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    const replacementQuestion: IExerciseQuestion = {
      ...replaceDto,
      id: existingQuestion.id, // Preserve ID
      order: existingQuestion.order, // Preserve order
      audit: {
        createdAt: existingQuestion.audit?.createdAt || new Date(),
        updatedAt: new Date(),
        createdBy: existingQuestion.audit?.createdBy || user.sub,
        updatedBy: user.sub,
        version: (existingQuestion.audit?.version || 1) + 1,
        changeLog: [
          ...(existingQuestion.audit?.changeLog || []),
          {
            timestamp: new Date(),
            userId: user.sub,
            action: 'full_replacement',
            changes: replaceDto,
            reason: replaceDto.updateReason
          }
        ]
      },
      schoolId: user.schoolId || undefined
    };

    return replacementQuestion;
  }

  /**
   * Validate answer format based on question type
   */
  private async validateAnswerFormat(type: string, answers: string[]): Promise<void> {
    switch (type) {
      case 'multiple_choice':
        if (answers.length === 0) {
          throw new BadRequestException('Multiple choice questions must have at least one correct answer');
        }
        break;
      case 'true_false':
        if (answers.length !== 1 || !['true', 'false'].includes(answers[0].toLowerCase())) {
          throw new BadRequestException('True/false questions must have exactly one answer: "true" or "false"');
        }
        break;
      case 'short_answer':
      case 'long_answer':
        if (answers.length === 0) {
          throw new BadRequestException('Answer questions must have at least one acceptable answer');
        }
        break;
      default:
        if (answers.length === 0) {
          throw new BadRequestException('Questions must have at least one correct answer');
        }
    }
  }

  /**
   * Validate question options
   */
  private async validateQuestionOptions(options: string[], type: string): Promise<void> {
    if (type === 'multiple_choice' && options.length < 2) {
      throw new BadRequestException('Multiple choice questions must have at least 2 options');
    }

    if (type === 'true_false' && options.length !== 2) {
      throw new BadRequestException('True/false questions must have exactly 2 options');
    }

    // Check for duplicate options
    const uniqueOptions = new Set(options.map(opt => opt.trim().toLowerCase()));
    if (uniqueOptions.size !== options.length) {
      throw new BadRequestException('Question options must be unique');
    }
  }

  /**
   * Validate image prompt
   */
  private async validateImagePrompt(imagePrompt: string): Promise<void> {
    // Check for measurement requirements
    const measurementKeywords = ['cm', 'mm', 'inch', 'meter', 'feet', 'dimension', 'size'];
    const hasSpecificMeasurements = measurementKeywords.some(keyword =>
      imagePrompt.toLowerCase().includes(keyword)
    );

    if (hasSpecificMeasurements) {
      // Validate that specific dimensions are provided
      const dimensionPattern = /\d+\s*(cm|mm|inch|meter|feet|px)/i;
      if (!dimensionPattern.test(imagePrompt)) {
        throw new BadRequestException(
          'Image prompts with measurement requirements must include specific dimensions (e.g., "10cm", "5 inches")'
        );
      }
    }

    // Check prompt length
    if (imagePrompt.length > 500) {
      throw new BadRequestException('Image prompt must be 500 characters or less');
    }
  }

  /**
   * Validate educational standards compliance
   */
  private async validateEducationalStandards(questionData: any): Promise<void> {
    // Validate grade level appropriateness
    if (questionData.grade) {
      const validGrades = ['K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];
      if (!validGrades.includes(questionData.grade)) {
        throw new BadRequestException(`Invalid grade level: ${questionData.grade}`);
      }
    }

    // Validate subject alignment
    if (questionData.subject && questionData.childSubject) {
      // Ensure child subject is related to parent subject
      await this.validateSubjectHierarchy(questionData.subject, questionData.childSubject);
    }

    // Validate content appropriateness
    if (questionData.content) {
      await this.validateContentAppropriatenesss(questionData.content, questionData.grade);
    }
  }

  /**
   * Validate subject hierarchy
   */
  private async validateSubjectHierarchy(parentSubject: string, childSubject: string): Promise<void> {
    // This would typically check against a database of valid subject hierarchies
    // For now, we'll do basic validation
    const subjectMappings = {
      'Mathematics': ['Algebra', 'Geometry', 'Calculus', 'Statistics', 'Arithmetic'],
      'Science': ['Physics', 'Chemistry', 'Biology', 'Earth Science'],
      'English': ['Literature', 'Grammar', 'Writing', 'Reading Comprehension'],
      'Social Studies': ['History', 'Geography', 'Civics', 'Economics']
    };

    if (subjectMappings[parentSubject] && !subjectMappings[parentSubject].includes(childSubject)) {
      this.logger.warn(`Potential subject hierarchy mismatch: ${parentSubject} -> ${childSubject}`);
    }
  }

  /**
   * Validate content appropriateness for grade level
   */
  private async validateContentAppropriatenesss(content: string, grade?: string): Promise<void> {
    if (!grade) return;

    // Basic content complexity validation based on grade
    const gradeNum = parseInt(grade) || 0;
    const wordCount = content.split(' ').length;
    const avgWordLength = content.split(' ').reduce((sum, word) => sum + word.length, 0) / wordCount;

    // Elementary grades (K-5) should have simpler content
    if (gradeNum <= 5) {
      if (wordCount > 50) {
        this.logger.warn(`Question content may be too long for grade ${grade}: ${wordCount} words`);
      }
      if (avgWordLength > 6) {
        this.logger.warn(`Question content may use complex words for grade ${grade}: avg ${avgWordLength} chars per word`);
      }
    }
  }

  /**
   * Validate worksheet version for optimistic locking
   */
  private async validateWorksheetVersion(worksheet: Worksheet, user: UserContext): Promise<void> {
    // Check if worksheet is locked by another user
    if (worksheet.questionMetadata?.lockStatus?.isLocked) {
      const lockedBy = worksheet.questionMetadata.lockStatus.lockedBy;
      if (lockedBy && lockedBy !== user.sub) {
        throw new ConflictException(
          `Worksheet is currently being edited by another user. Please try again later.`
        );
      }
    }

    // For now, we'll implement basic version checking
    // In a more advanced implementation, you might want to check specific version numbers
    const lastModified = worksheet.questionMetadata?.lastQuestionUpdate;
    if (lastModified) {
      const timeDiff = Date.now() - lastModified.getTime();
      // If worksheet was modified in the last 5 seconds by someone else, consider it a conflict
      if (timeDiff < 5000 && worksheet.lastModifiedBy !== user.sub) {
        throw new ConflictException(
          'Worksheet has been recently modified by another user. Please refresh and try again.'
        );
      }
    }
  }

  /**
   * Validate reorder operations
   */
  private async validateReorderOperations(
    worksheet: Worksheet,
    reorders: ReorderQuestionDto[]
  ): Promise<void> {
    if (!worksheet.questions || worksheet.questions.length === 0) {
      throw new BadRequestException('Worksheet has no questions to reorder');
    }

    const totalQuestions = worksheet.questions.length;
    const questionIds = worksheet.questions.map(q => q.id);
    const newPositions = new Set<number>();

    // Validate each reorder operation
    for (const reorder of reorders) {
      // Check if question exists in worksheet
      if (!questionIds.includes(reorder.questionId)) {
        throw new NotFoundException(`Question ${reorder.questionId} not found in worksheet`);
      }

      // Check if new position is valid
      if (reorder.newPosition < 1 || reorder.newPosition > totalQuestions) {
        throw new BadRequestException(
          `Invalid position ${reorder.newPosition}. Must be between 1 and ${totalQuestions}`
        );
      }

      // Check for duplicate positions
      if (newPositions.has(reorder.newPosition)) {
        throw new BadRequestException(
          `Duplicate position ${reorder.newPosition} found in reorder operations`
        );
      }
      newPositions.add(reorder.newPosition);
    }

    // The reordering logic will automatically handle position shifting,
    // so we don't need to validate position gaps here
  }

  /**
   * Perform the actual question reordering
   */
  private async performQuestionReordering(
    worksheet: Worksheet,
    reorders: ReorderQuestionDto[],
    user: UserContext
  ): Promise<Array<{
    questionId: string;
    oldPosition: number;
    newPosition: number;
  }>> {
    const results: Array<{
      questionId: string;
      oldPosition: number;
      newPosition: number;
    }> = [];

    // Track old positions before reordering
    const oldPositions = new Map<string, number>();
    worksheet.questions.forEach((question, index) => {
      if (question.id) {
        const currentPosition = question.order || (index + 1);
        oldPositions.set(question.id, currentPosition);
        question.order = currentPosition; // Ensure all questions have an order
      }
    });

    // Process each reorder operation
    for (const reorder of reorders) {
      const questionToMove = worksheet.questions.find(q => q.id === reorder.questionId);
      if (!questionToMove) continue;

      const oldPosition = oldPositions.get(reorder.questionId) || 0;
      const newPosition = reorder.newPosition;

      if (oldPosition === newPosition) continue; // No change needed

      // Remove the question from its current position
      const questionIndex = worksheet.questions.findIndex(q => q.id === reorder.questionId);
      const [movedQuestion] = worksheet.questions.splice(questionIndex, 1);

      // Insert the question at the new position (convert to 0-based index)
      worksheet.questions.splice(newPosition - 1, 0, movedQuestion);

      // Update the moved question's order
      movedQuestion.order = newPosition;

      // Update audit information for the moved question
      if (movedQuestion.audit) {
        movedQuestion.audit.updatedBy = user.sub;
        movedQuestion.audit.updatedAt = new Date();
        movedQuestion.audit.version = (movedQuestion.audit.version || 1) + 1;
        movedQuestion.audit.changeLog = [
          ...(movedQuestion.audit.changeLog || []),
          {
            timestamp: new Date(),
            userId: user.sub,
            action: 'reorder',
            changes: { oldPosition, newPosition },
            reason: 'Question reordered'
          }
        ];
      }

      results.push({
        questionId: reorder.questionId,
        oldPosition,
        newPosition
      });
    }

    // Renumber all questions to ensure sequential order
    worksheet.questions.forEach((question, index) => {
      question.order = index + 1;
    });

    // Update worksheet metadata
    worksheet.lastModifiedBy = user.sub;
    worksheet.questionMetadata = {
      ...worksheet.questionMetadata,
      lastQuestionUpdate: new Date(),
      questionVersion: (worksheet.questionMetadata?.questionVersion || 1) + 1,
      hasUnsavedChanges: false
    };

    return results;
  }

  /**
   * Bulk add questions to a worksheet (synchronous operation)
   */
  async bulkAddQuestions(
    worksheetId: string,
    questions: AddQuestionToWorksheetDto[],
    user: UserContext,
    options: {
      insertPosition?: number;
      validateQuestions?: boolean;
      reason?: string;
    } = {}
  ): Promise<{
    success: boolean;
    successCount: number;
    failureCount: number;
    totalCount: number;
    successes: IExerciseQuestion[];
    failures: Array<{ item: AddQuestionToWorksheetDto; error: string; index: number }>;
    processingTimeMs: number;
  }> {
    const startTime = Date.now();
    this.logger.log(`Bulk adding ${questions.length} questions to worksheet ${worksheetId} by user ${user.sub}`);

    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Check if bulk addition would exceed question limit
    const currentCount = worksheet.questions?.length || 0;
    const maxQuestions = worksheet.maxQuestions || 100;
    if (currentCount + questions.length > maxQuestions) {
      throw new BadRequestException(
        `Bulk addition would exceed question limit. Current: ${currentCount}, Adding: ${questions.length}, Maximum: ${maxQuestions}`
      );
    }

    const successes: IExerciseQuestion[] = [];
    const failures: Array<{ item: AddQuestionToWorksheetDto; error: string; index: number }> = [];

    // Process each question
    for (let i = 0; i < questions.length; i++) {
      try {
        const questionDto = questions[i];
        const newQuestion = await this.createQuestion(questionDto, user, worksheet);

        // Add to worksheet
        if (!worksheet.questions) {
          worksheet.questions = [];
        }

        // Insert at specified position or at the end
        if (options.insertPosition && i === 0) {
          worksheet.questions.splice(options.insertPosition - 1, 0, newQuestion);
        } else {
          worksheet.questions.push(newQuestion);
        }

        successes.push(newQuestion);
      } catch (error) {
        failures.push({
          item: questions[i],
          error: error.message,
          index: i
        });
      }
    }

    // Update worksheet metadata
    worksheet.totalQuestions = worksheet.questions.length;
    worksheet.lastModifiedBy = user.sub;

    // Save worksheet
    await this.worksheetRepository.save(worksheet);

    // Update cache
    await this.updateQuestionCache(worksheetId, worksheet.questions, user);

    // Emit real-time updates
    await this.emitQuestionUpdate(worksheetId, 'questions_bulk_added', {
      addedQuestions: successes,
      totalQuestions: worksheet.questions.length,
      worksheetId,
      reason: options.reason
    }, user.sub);

    // Log audit event
    await this.auditService.logBulkQuestionOperation(
      worksheetId,
      'bulk_add',
      user,
      { successCount: successes.length, failureCount: failures.length, reason: options.reason }
    );

    const processingTimeMs = Date.now() - startTime;
    this.logger.log(`Bulk add completed: ${successes.length} successes, ${failures.length} failures in ${processingTimeMs}ms`);

    return {
      success: failures.length === 0,
      successCount: successes.length,
      failureCount: failures.length,
      totalCount: questions.length,
      successes,
      failures,
      processingTimeMs
    };
  }

  /**
   * Bulk remove questions from a worksheet (synchronous operation)
   */
  async bulkRemoveQuestions(
    worksheetId: string,
    questionIds: string[],
    user: UserContext,
    options: {
      forceRemoval?: boolean;
      reason?: string;
    } = {}
  ): Promise<{
    success: boolean;
    successCount: number;
    failureCount: number;
    totalCount: number;
    successes: string[];
    failures: Array<{ questionId: string; error: string; index: number }>;
    processingTimeMs: number;
  }> {
    const startTime = Date.now();
    this.logger.log(`Bulk removing ${questionIds.length} questions from worksheet ${worksheetId} by user ${user.sub}`);

    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Check minimum questions requirement unless forced
    if (!options.forceRemoval) {
      const currentCount = worksheet.questions?.length || 0;
      const remainingCount = currentCount - questionIds.length;
      if (remainingCount < 1) {
        throw new BadRequestException(
          `Cannot remove ${questionIds.length} questions. Worksheet must have at least 1 question. Current count: ${currentCount}`
        );
      }
    }

    const successes: string[] = [];
    const failures: Array<{ questionId: string; error: string; index: number }> = [];

    // Process each question ID
    for (let i = 0; i < questionIds.length; i++) {
      try {
        const questionId = questionIds[i];

        // Find question in worksheet
        const questionIndex = worksheet.questions?.findIndex(q => q.id === questionId);
        if (questionIndex === undefined || questionIndex === -1) {
          throw new Error(`Question with ID ${questionId} not found in worksheet`);
        }

        // Remove the question
        worksheet.questions.splice(questionIndex, 1);
        successes.push(questionId);
      } catch (error) {
        failures.push({
          questionId: questionIds[i],
          error: error.message,
          index: i
        });
      }
    }

    // Reorder remaining questions
    worksheet.questions?.forEach((question, index) => {
      question.order = index + 1;
      if (question.audit) {
        question.audit.updatedBy = user.sub;
        question.audit.updatedAt = new Date();
        question.audit.version = (question.audit.version || 1) + 1;
      }
    });

    // Update worksheet metadata
    worksheet.totalQuestions = worksheet.questions?.length || 0;
    worksheet.lastModifiedBy = user.sub;

    // Save worksheet
    await this.worksheetRepository.save(worksheet);

    // Update cache
    await this.updateQuestionCache(worksheetId, worksheet.questions || [], user);

    // Emit real-time updates
    await this.emitQuestionUpdate(worksheetId, 'questions_bulk_removed', {
      removedQuestionIds: successes,
      totalQuestions: worksheet.questions?.length || 0,
      worksheetId,
      reason: options.reason
    }, user.sub);

    // Log audit event
    await this.auditService.logBulkQuestionOperation(
      worksheetId,
      'bulk_remove',
      user,
      { successCount: successes.length, failureCount: failures.length, reason: options.reason }
    );

    const processingTimeMs = Date.now() - startTime;
    this.logger.log(`Bulk remove completed: ${successes.length} successes, ${failures.length} failures in ${processingTimeMs}ms`);

    return {
      success: failures.length === 0,
      successCount: successes.length,
      failureCount: failures.length,
      totalCount: questionIds.length,
      successes,
      failures,
      processingTimeMs
    };
  }

  /**
   * Bulk update questions in a worksheet (synchronous operation)
   */
  async bulkUpdateQuestions(
    worksheetId: string,
    updates: Array<{ questionId: string; updates: UpdateWorksheetQuestionDto }>,
    user: UserContext,
    options: {
      validateQuestions?: boolean;
      reason?: string;
    } = {}
  ): Promise<{
    success: boolean;
    successCount: number;
    failureCount: number;
    totalCount: number;
    successes: IExerciseQuestion[];
    failures: Array<{ item: { questionId: string; updates: UpdateWorksheetQuestionDto }; error: string; index: number }>;
    processingTimeMs: number;
  }> {
    const startTime = Date.now();
    this.logger.log(`Bulk updating ${updates.length} questions in worksheet ${worksheetId} by user ${user.sub}`);

    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    const successes: IExerciseQuestion[] = [];
    const failures: Array<{ item: { questionId: string; updates: UpdateWorksheetQuestionDto }; error: string; index: number }> = [];

    // Process each update
    for (let i = 0; i < updates.length; i++) {
      try {
        const { questionId, updates: updateDto } = updates[i];

        // Find question in worksheet
        const questionIndex = worksheet.questions?.findIndex(q => q.id === questionId);
        if (questionIndex === undefined || questionIndex === -1) {
          throw new Error(`Question with ID ${questionId} not found in worksheet`);
        }

        const existingQuestion = worksheet.questions[questionIndex];

        // Validate optimistic locking if version is provided
        if (updateDto.version !== undefined) {
          await this.validateQuestionVersion(existingQuestion, updateDto.version);
        }

        // Apply the update
        const updatedQuestion = await this.applyQuestionUpdate(existingQuestion, updateDto, user);
        worksheet.questions[questionIndex] = updatedQuestion;

        successes.push(updatedQuestion);
      } catch (error) {
        failures.push({
          item: updates[i],
          error: error.message,
          index: i
        });
      }
    }

    // Update worksheet metadata
    worksheet.lastModifiedBy = user.sub;

    // Save worksheet
    await this.worksheetRepository.save(worksheet);

    // Update cache
    await this.updateQuestionCache(worksheetId, worksheet.questions || [], user);

    // Emit real-time updates
    await this.emitQuestionUpdate(worksheetId, 'questions_bulk_updated', {
      updatedQuestions: successes,
      totalQuestions: worksheet.questions?.length || 0,
      worksheetId,
      reason: options.reason
    }, user.sub);

    // Log audit event
    await this.auditService.logBulkQuestionOperation(
      worksheetId,
      'bulk_update',
      user,
      { successCount: successes.length, failureCount: failures.length, reason: options.reason }
    );

    const processingTimeMs = Date.now() - startTime;
    this.logger.log(`Bulk update completed: ${successes.length} successes, ${failures.length} failures in ${processingTimeMs}ms`);

    return {
      success: failures.length === 0,
      successCount: successes.length,
      failureCount: failures.length,
      totalCount: updates.length,
      successes,
      failures,
      processingTimeMs
    };
  }

  /**
   * Emit real-time update via WebSocket
   */
  private async emitQuestionUpdate(
    worksheetId: string,
    event: string,
    data: any,
    excludeUserId?: string
  ): Promise<void> {
    try {
      // Emit to all users subscribed to this worksheet except the one who made the change
      this.socketGateway.server.to(`worksheet-${worksheetId}`).emit(event, {
        ...data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      this.logger.error(`Failed to emit ${event} for worksheet ${worksheetId}`, error);
      // Don't throw error - WebSocket failure shouldn't fail the operation
    }
  }

  /**
   * Validate user access to a pool question
   */
  private validatePoolQuestionAccess(poolQuestion: QuestionPool, user: UserContext): void {
    // Admin can access all questions
    if (user.role === EUserRole.ADMIN) {
      return;
    }

    // For now, all active questions in the pool are accessible to authenticated users
    // In the future, you could implement more granular access control based on:
    // - School-specific question pools
    // - Public/private question visibility
    // - User role-based restrictions

    if (poolQuestion.status !== 'active') {
      throw new BadRequestException('Question is not available for use');
    }
  }

  /**
   * Update pool question usage statistics
   */
  private async updatePoolQuestionUsage(questionPoolId: string): Promise<void> {
    try {
      // This could be implemented to track usage statistics
      // For now, we'll just log the usage
      this.logger.debug(`Question ${questionPoolId} used from pool`);

      // In the future, you could:
      // - Increment usage count in the pool question
      // - Track last used timestamp
      // - Update popularity metrics
      // await this.questionPoolService.incrementUsageCount(questionPoolId);
    } catch (error) {
      this.logger.error(`Failed to update pool question usage for ${questionPoolId}`, error);
      // Don't throw error - usage tracking failure shouldn't fail the operation
    }
  }
}
