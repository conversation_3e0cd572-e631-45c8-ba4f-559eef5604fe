import {
  Controller,
  Post,
  Delete,
  Patch,
  Put,
  Body,
  Param,
  UseGuards,
  UseInterceptors,
  HttpCode,
  HttpStatus,
  Logger
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiResponse,
  ApiBearerAuth
} from '@nestjs/swagger';
import { Roles } from '../../auth/decorators/role.decorator';
import { EUserRole } from '../../user/dto/create-user.dto';
import { ActiveUser } from '../../auth/decorators/active-user.decorator';
import { User } from '../../user/entities/user.entity';
import { WorksheetQuestionService } from '../services/worksheet-question.service';
import { WorksheetQuestionThrottlerGuard } from '../guards/worksheet-question-throttler.guard';
import { WorksheetQuestionMetricsInterceptor } from '../interceptors/worksheet-question-metrics.interceptor';
import {
  AddQuestionDto,
  UpdateQuestionDto,
  ReplaceQuestionDto,
  RemoveQuestionDto,
  BulkReorderQuestionsDto,
  BulkAddQuestionsDto,
  BulkRemoveQuestionsDto,
  BulkUpdateQuestionsDto
} from '../dto/question.dto';
import { BulkOperationResponseDto } from '../dto/bulk-operations.dto';

@ApiTags('Questions')
@ApiBearerAuth()
@Controller('questions')
export class QuestionController {
  private readonly logger = new Logger(QuestionController.name);

  constructor(
    private readonly worksheetQuestionService: WorksheetQuestionService
  ) {}

  @Post()
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Add a question to a worksheet',
    description: `Add a new question to an existing worksheet with comprehensive validation and access control.

    **Access Control:**
    - **Admin users**: Can add questions to any worksheet
    - **School Manager**: Can add questions to worksheets in their school only
    - **Teacher**: Can add questions to worksheets in their school only
    - **Independent Teacher**: Can add questions to their own worksheets only

    **Validation:**
    - Validates question data structure and required fields
    - Enforces question limit (max 100 questions per worksheet)
    - Ensures school-based data isolation

    **Features:**
    - Real-time updates via WebSocket
    - Audit logging for compliance
    - MongoDB cache synchronization
    - Comprehensive error handling`
  })
  @ApiBody({ type: AddQuestionDto })
  @ApiResponse({
    status: 201,
    description: 'Question added successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          description: 'The created question object'
        },
        message: { type: 'string', example: 'Question added successfully' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid question data or limit exceeded' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Concurrent modification detected' })
  async addQuestion(
    @Body() questionDto: AddQuestionDto,
    @ActiveUser() user: User
  ) {
    try {
      const { worksheetId, ...questionData } = questionDto;
      
      const question = await this.worksheetQuestionService.addQuestionToWorksheet(
        worksheetId,
        questionData,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId || undefined
        }
      );

      return {
        success: true,
        data: question,
        message: 'Question added successfully'
      };
    } catch (error) {
      this.logger.error(`Error adding question to worksheet ${questionDto.worksheetId}:`, error);
      throw error;
    }
  }

  @Delete(':questionId')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Remove a question from a worksheet',
    description: `Remove an existing question from a worksheet with comprehensive validation and access control.

    **Access Control:**
    - **Admin users**: Can remove questions from any worksheet
    - **School Manager**: Can remove questions from worksheets in their school only
    - **Teacher**: Can remove questions from worksheets in their school only
    - **Independent Teacher**: Can remove questions from their own worksheets only

    **Validation:**
    - Validates that the question exists in the specified worksheet
    - Prevents removal if it would result in zero questions (minimum 1 required)
    - Ensures school-based data isolation
    - Validates user permissions before allowing removal

    **Side Effects:**
    - Reorders remaining questions to maintain sequential order
    - Updates MongoDB cache for the worksheet
    - Emits real-time WebSocket event to notify collaborators
    - Creates audit log entry for the removal
    - Updates worksheet metadata (totalQuestions, lastModifiedBy)`
  })
  @ApiParam({ name: 'questionId', description: 'Question ID to remove' })
  @ApiBody({ type: RemoveQuestionDto })
  @ApiResponse({ status: 204, description: 'Question removed successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - Cannot remove last question or invalid parameters' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet or question not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Concurrent modification detected' })
  async removeQuestion(
    @Param('questionId') questionId: string,
    @Body() removeDto: RemoveQuestionDto,
    @ActiveUser() user: User
  ): Promise<void> {
    try {
      await this.worksheetQuestionService.removeQuestionFromWorksheet(
        removeDto.worksheetId,
        questionId,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId || undefined
        }
      );
    } catch (error) {
      this.logger.error(`Failed to remove question ${questionId} from worksheet ${removeDto.worksheetId}`, error);
      throw error;
    }
  }

  @Patch(':questionId')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Update a question in a worksheet (partial update)',
    description: `Partially update an existing question in a worksheet with comprehensive validation and access control.

    **Access Control:**
    - **Admin users**: Can update questions in any worksheet
    - **School Manager**: Can update questions in worksheets in their school only
    - **Teacher**: Can update questions in worksheets in their school only
    - **Independent Teacher**: Can update questions in their own worksheets only

    **Features:**
    - Partial updates - only provided fields are updated
    - Optimistic locking using version numbers to prevent concurrent conflicts
    - Comprehensive validation including educational standards compliance
    - Real-time WebSocket notifications to collaborators
    - Detailed audit logging with change tracking
    - MongoDB cache synchronization
    - Version history tracking

    **Validation:**
    - Validates question data structure and educational standards
    - Checks answer format based on question type
    - Validates image prompt requirements (dimensions, measurements)
    - Ensures school-based data isolation
    - Prevents concurrent modification conflicts`
  })
  @ApiParam({ name: 'questionId', description: 'Question ID to update' })
  @ApiBody({ type: UpdateQuestionDto })
  @ApiResponse({
    status: 200,
    description: 'Question updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          description: 'The updated question object'
        },
        message: { type: 'string', example: 'Question updated successfully' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid question data or validation errors' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet or question not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Concurrent modification detected (version mismatch)' })
  async updateQuestion(
    @Param('questionId') questionId: string,
    @Body() updateDto: UpdateQuestionDto,
    @ActiveUser() user: User
  ) {
    try {
      const { worksheetId, ...updateData } = updateDto;

      const updatedQuestion = await this.worksheetQuestionService.updateQuestionInWorksheet(
        worksheetId!,
        questionId,
        updateData,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId || undefined
        }
      );

      return {
        success: true,
        data: updatedQuestion,
        message: 'Question updated successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to update question ${questionId} in worksheet ${updateDto.worksheetId}`, error);
      throw error;
    }
  }

  @Put(':questionId')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Replace a question in a worksheet (full replacement)',
    description: `Completely replace an existing question in a worksheet with comprehensive validation and access control.`
  })
  @ApiParam({ name: 'questionId', description: 'Question ID to replace' })
  @ApiBody({ type: ReplaceQuestionDto })
  @ApiResponse({
    status: 200,
    description: 'Question replaced successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          description: 'The replaced question object'
        },
        message: { type: 'string', example: 'Question replaced successfully' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid question data or validation errors' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet or question not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Concurrent modification detected (version mismatch)' })
  async replaceQuestion(
    @Param('questionId') questionId: string,
    @Body() replaceDto: ReplaceQuestionDto,
    @ActiveUser() user: User
  ) {
    try {
      const { worksheetId, ...replaceData } = replaceDto;

      const replacedQuestion = await this.worksheetQuestionService.replaceQuestionInWorksheet(
        worksheetId!,
        questionId,
        replaceData,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId || undefined
        }
      );

      return {
        success: true,
        data: replacedQuestion,
        message: 'Question replaced successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to replace question ${questionId} in worksheet ${replaceDto.worksheetId}`, error);
      throw error;
    }
  }

  @Patch('reorder')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Reorder questions in a worksheet',
    description: `Reorder questions within a worksheet, supporting both single and bulk operations with comprehensive validation and access control.

    **Access Control:**
    - **Admin users**: Can reorder questions in any worksheet
    - **School Manager**: Can reorder questions in worksheets in their school only
    - **Teacher**: Can reorder questions in worksheets in their school only
    - **Independent Teacher**: Can reorder questions in their own worksheets only

    **Features:**
    - Supports bulk reordering of multiple questions
    - Validates all question IDs belong to the specified worksheet
    - Ensures new positions are valid within the worksheet's question count
    - Implements optimistic locking to prevent race conditions
    - Updates question order in PostgreSQL database
    - Synchronizes changes with MongoDB cache
    - Emits real-time WebSocket notifications
    - Creates comprehensive audit log entries

    **Validation:**
    - All provided question IDs must exist in the worksheet
    - New positions must be within valid range (1 to total questions)
    - No duplicate positions allowed
    - Optimistic locking prevents concurrent modifications
    - School-based data isolation enforced`
  })
  @ApiBody({ type: BulkReorderQuestionsDto })
  @ApiResponse({
    status: 200,
    description: 'Questions reordered successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Questions reordered successfully' },
        data: {
          type: 'object',
          properties: {
            worksheetId: { type: 'string', example: 'worksheet-123' },
            totalQuestions: { type: 'number', example: 10 },
            reorderedQuestions: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  questionId: { type: 'string', example: 'question-123' },
                  oldPosition: { type: 'number', example: 5 },
                  newPosition: { type: 'number', example: 3 }
                }
              }
            },
            version: { type: 'number', example: 15 }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid question IDs, positions, or validation errors' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet not found or question IDs not found in worksheet' })
  @ApiResponse({ status: 409, description: 'Conflict - Concurrent modification detected (optimistic locking)' })
  async reorderQuestions(
    @Body() reorderDto: BulkReorderQuestionsDto,
    @ActiveUser() user: User
  ) {
    try {
      // Debug logging
      this.logger.debug(`Reorder request received for worksheet ${reorderDto.worksheetId}`, {
        reorderDto,
        reordersCount: reorderDto?.reorders?.length || 0
      });

      const result = await this.worksheetQuestionService.reorderQuestionsInWorksheet(
        reorderDto.worksheetId,
        { reorders: reorderDto.reorders },
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId || undefined
        }
      );

      return {
        success: true,
        data: result,
        message: 'Questions reordered successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to reorder questions in worksheet ${reorderDto.worksheetId}`, error);
      throw error;
    }
  }

  @Post('bulk-add')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Bulk add questions to a worksheet',
    description: `Add multiple questions to a worksheet in a single operation with comprehensive validation and access control.`
  })
  @ApiBody({ type: BulkAddQuestionsDto })
  @ApiResponse({
    status: 201,
    description: 'Bulk add operation completed',
    type: BulkOperationResponseDto
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data or question limit exceeded' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet not found' })
  async bulkAddQuestions(
    @Body() bulkAddDto: BulkAddQuestionsDto,
    @ActiveUser() user: User
  ): Promise<BulkOperationResponseDto> {
    const startTime = Date.now();

    try {
      const { worksheetId, questions, ...options } = bulkAddDto;

      const result = await this.worksheetQuestionService.bulkAddQuestions(
        worksheetId!,
        questions,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId
        },
        options
      );

      return {
        success: result.success,
        successCount: result.successCount,
        failureCount: result.failureCount,
        totalCount: result.totalCount,
        successes: result.successes,
        failures: result.failures,
        timestamp: new Date().toISOString(),
        processingTimeMs: Date.now() - startTime
      };
    } catch (error) {
      throw error;
    }
  }

  @Delete('bulk-remove')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Bulk remove questions from a worksheet',
    description: `Remove multiple questions from a worksheet in a single operation with comprehensive validation and access control.`
  })
  @ApiBody({ type: BulkRemoveQuestionsDto })
  @ApiResponse({
    status: 200,
    description: 'Bulk remove operation completed',
    type: BulkOperationResponseDto
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data or minimum question violation' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet not found' })
  async bulkRemoveQuestions(
    @Body() bulkRemoveDto: BulkRemoveQuestionsDto,
    @ActiveUser() user: User
  ): Promise<BulkOperationResponseDto> {
    const startTime = Date.now();

    try {
      const { worksheetId, questionIds, ...options } = bulkRemoveDto;

      const result = await this.worksheetQuestionService.bulkRemoveQuestions(
        worksheetId,
        questionIds,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId
        },
        options
      );

      return {
        success: result.success,
        successCount: result.successCount,
        failureCount: result.failureCount,
        totalCount: result.totalCount,
        successes: result.successes,
        failures: result.failures,
        timestamp: new Date().toISOString(),
        processingTimeMs: Date.now() - startTime
      };
    } catch (error) {
      throw error;
    }
  }

  @Patch('bulk-update')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Bulk update questions in a worksheet',
    description: `Update multiple questions in a worksheet in a single operation with comprehensive validation and access control.`
  })
  @ApiBody({ type: BulkUpdateQuestionsDto })
  @ApiResponse({
    status: 200,
    description: 'Bulk update operation completed',
    type: BulkOperationResponseDto
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Version mismatch (optimistic locking)' })
  async bulkUpdateQuestions(
    @Body() bulkUpdateDto: BulkUpdateQuestionsDto,
    @ActiveUser() user: User
  ): Promise<BulkOperationResponseDto> {
    const startTime = Date.now();

    try {
      const { worksheetId, updates, ...options } = bulkUpdateDto;

      const result = await this.worksheetQuestionService.bulkUpdateQuestions(
        worksheetId,
        updates,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId
        },
        options
      );

      return {
        success: result.success,
        successCount: result.successCount,
        failureCount: result.failureCount,
        totalCount: result.totalCount,
        successes: result.successes,
        failures: result.failures,
        timestamp: new Date().toISOString(),
        processingTimeMs: Date.now() - startTime
      };
    } catch (error) {
      throw error;
    }
  }
}
