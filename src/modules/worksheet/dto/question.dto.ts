import {
  IsString,
  IsArray,
  IsOptional,
  IsNumber,
  IsBoolean,
  IsUUID,
  ArrayMinSize,
  ArrayMaxSize,
  MaxLength,
  Min,
  <PERSON>,
  ValidateNested,
  IsNotEmpty
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { CreateExerciseQuestionDto } from './exercise-question.dto';

/**
 * DTO for adding a question to a worksheet via Question Controller
 */
export class AddQuestionDto extends CreateExerciseQuestionDto {
  @ApiProperty({
    description: 'Worksheet ID to add the question to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  worksheetId: string;

  @ApiPropertyOptional({
    description: 'Position in worksheet (if not provided, will be added at the end)',
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  position?: number;

  @ApiPropertyOptional({
    description: 'Points allocated for this question',
    minimum: 0,
    maximum: 100,
    default: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  points?: number;

  @ApiPropertyOptional({
    description: 'Database option type ID for categorizing the question context',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  optionTypeId?: string;

  @ApiPropertyOptional({
    description: 'Database option value ID for specific option selection within the type',
    example: '987fcdeb-51a2-43d1-b789-123456789abc'
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  optionValueId?: string;

  @ApiPropertyOptional({
    description: 'Question Pool ID - if provided, the question will be copied from the question pool',
    example: '507f1f77bcf86cd799439011'
  })
  @IsOptional()
  @IsString()
  questionPoolId?: string;
}

/**
 * DTO for updating a question (PATCH - partial updates)
 */
export class UpdateQuestionDto extends PartialType(AddQuestionDto) {
  @ApiPropertyOptional({
    description: 'Version number for optimistic locking',
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  version?: number;

  @ApiPropertyOptional({
    description: 'Reason for the update (for audit purposes)'
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  updateReason?: string;
}

/**
 * DTO for full question replacement (PUT)
 */
export class ReplaceQuestionDto extends AddQuestionDto {
  @ApiProperty({
    description: 'Version number for optimistic locking',
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  version: number;

  @ApiPropertyOptional({
    description: 'Reason for the replacement (for audit purposes)'
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  updateReason?: string;
}

/**
 * DTO for removing a question
 */
export class RemoveQuestionDto {
  @ApiProperty({
    description: 'Worksheet ID containing the question',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  worksheetId: string;

  @ApiPropertyOptional({
    description: 'Reason for removal (for audit purposes)'
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;
}

/**
 * DTO for reordering questions
 */
export class ReorderQuestionDto {
  @ApiProperty({ 
    description: 'Question ID to reorder' 
  })
  @IsString()
  @IsNotEmpty()
  questionId: string;

  @ApiProperty({ 
    description: 'New position for the question',
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  newPosition: number;
}

/**
 * DTO for bulk reordering questions
 */
export class BulkReorderQuestionsDto {
  @ApiProperty({
    description: 'Worksheet ID containing the questions to reorder',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  worksheetId: string;

  @ApiProperty({
    description: 'Array of question reorder operations',
    type: [ReorderQuestionDto],
    example: [
      {
        questionId: 'question-123',
        newPosition: 1
      }
    ]
  })
  @IsArray({ message: 'reorders must be an array' })
  @ArrayMinSize(1, { message: 'At least one reorder operation is required' })
  @ArrayMaxSize(100, { message: 'Maximum 100 reorder operations allowed' })
  @ValidateNested({ each: true, message: 'Each reorder operation must be valid' })
  @Type(() => ReorderQuestionDto)
  reorders: ReorderQuestionDto[];
}

/**
 * DTO for bulk adding questions
 */
export class BulkAddQuestionsDto {
  @ApiProperty({
    description: 'Worksheet ID to add questions to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  worksheetId: string;

  @ApiProperty({ 
    description: 'Array of questions to add to the worksheet',
    type: [CreateExerciseQuestionDto],
    minItems: 1,
    maxItems: 50
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  @ValidateNested({ each: true })
  @Type(() => CreateExerciseQuestionDto)
  questions: CreateExerciseQuestionDto[];

  @ApiPropertyOptional({ 
    description: 'Position to insert questions (if not provided, will be added at the end)',
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  insertPosition?: number;

  @ApiPropertyOptional({ 
    description: 'Whether to validate questions before adding',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  validateQuestions?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Reason for bulk addition (for audit purposes)' 
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;
}

/**
 * DTO for bulk removing questions
 */
export class BulkRemoveQuestionsDto {
  @ApiProperty({
    description: 'Worksheet ID containing the questions to remove',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  worksheetId: string;

  @ApiProperty({
    description: 'Question IDs to remove from the worksheet',
    type: [String],
    minItems: 1,
    maxItems: 50
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  questionIds: string[];

  @ApiPropertyOptional({
    description: 'Reason for bulk removal (for audit purposes)'
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;

  @ApiPropertyOptional({
    description: 'Whether to force removal even if it would leave the worksheet with no questions',
    default: false
  })
  @IsOptional()
  @IsBoolean()
  forceRemoval?: boolean = false;
}

/**
 * Individual question update item for bulk operations
 */
export class BulkQuestionUpdateItem {
  @ApiProperty({
    description: 'Question ID to update'
  })
  @IsString()
  @IsNotEmpty()
  questionId: string;

  @ApiProperty({
    description: 'Question updates to apply',
    type: UpdateQuestionDto
  })
  @ValidateNested()
  @Type(() => UpdateQuestionDto)
  updates: Partial<UpdateQuestionDto>;
}

/**
 * DTO for bulk updating questions
 */
export class BulkUpdateQuestionsDto {
  @ApiProperty({
    description: 'Worksheet ID containing the questions to update',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  worksheetId: string;

  @ApiProperty({
    description: 'Array of question updates',
    minItems: 1,
    maxItems: 50
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  @ValidateNested({ each: true })
  @Type(() => BulkQuestionUpdateItem)
  updates: BulkQuestionUpdateItem[];

  @ApiPropertyOptional({
    description: 'Reason for bulk update (for audit purposes)'
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;

  @ApiPropertyOptional({
    description: 'Whether to validate questions after updating',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  validateQuestions?: boolean = true;
}
