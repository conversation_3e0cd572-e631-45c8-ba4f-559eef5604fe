# Question Controller Migration Guide

## Overview

We've created a new dedicated Question Controller to handle all question-related operations. This provides cleaner API structure and resolves validation issues with the previous nested worksheet endpoints.

## New API Structure

### Before (Worksheet Controller)
```
POST   /worksheets/:id/questions
DELETE /worksheets/:id/questions/:questionId
PATCH  /worksheets/:id/questions/:questionId
PUT    /worksheets/:id/questions/:questionId
PATCH  /worksheets/:id/questions/reorder
POST   /worksheets/:id/questions/bulk-add
DELETE /worksheets/:id/questions/bulk-remove
PATCH  /worksheets/:id/questions/bulk-update
```

### After (Question Controller)
```
POST   /questions
DELETE /questions/:questionId
PATCH  /questions/:questionId
PUT    /questions/:questionId
PATCH  /questions/reorder
POST   /questions/bulk-add
DELETE /questions/bulk-remove
PATCH  /questions/bulk-update
```

## Key Changes

### 1. **worksheetId in Request Body**
All endpoints now require `worksheetId` in the request body instead of URL parameter.

### 2. **Simplified URL Structure**
No more nested URLs - all question operations are under `/questions`

### 3. **Cleaner Validation**
Resolved validation conflicts by having all data in request body

## API Endpoint Details

### Add Question
```typescript
// Before
POST /worksheets/{worksheetId}/questions
Body: { type, content, options, answer, explain, ... }

// After  
POST /questions
Body: { 
  worksheetId: "uuid",
  type, content, options, answer, explain, ...
}
```

### Remove Question
```typescript
// Before
DELETE /worksheets/{worksheetId}/questions/{questionId}
Body: { reason?: string }

// After
DELETE /questions/{questionId}
Body: { 
  worksheetId: "uuid",
  reason?: string 
}
```

### Update Question
```typescript
// Before
PATCH /worksheets/{worksheetId}/questions/{questionId}
Body: { content?, options?, ... }

// After
PATCH /questions/{questionId}
Body: { 
  worksheetId: "uuid",
  content?, options?, ...
}
```

### Replace Question
```typescript
// Before
PUT /worksheets/{worksheetId}/questions/{questionId}
Body: { type, content, options, answer, explain, version, ... }

// After
PUT /questions/{questionId}
Body: { 
  worksheetId: "uuid",
  type, content, options, answer, explain, version, ...
}
```

### Reorder Questions (FIXED!)
```typescript
// Before
PATCH /worksheets/{worksheetId}/questions/reorder
Body: { 
  reorders: [
    { questionId: "q1", newPosition: 1 },
    { questionId: "q2", newPosition: 2 }
  ]
}

// After
PATCH /questions/reorder
Body: { 
  worksheetId: "uuid",
  reorders: [
    { questionId: "q1", newPosition: 1 },
    { questionId: "q2", newPosition: 2 }
  ]
}
```

### Bulk Add Questions
```typescript
// Before
POST /worksheets/{worksheetId}/questions/bulk-add
Body: { 
  questions: [...],
  insertPosition?: number,
  validateQuestions?: boolean,
  reason?: string
}

// After
POST /questions/bulk-add
Body: { 
  worksheetId: "uuid",
  questions: [...],
  insertPosition?: number,
  validateQuestions?: boolean,
  reason?: string
}
```

### Bulk Remove Questions
```typescript
// Before
DELETE /worksheets/{worksheetId}/questions/bulk-remove
Body: { 
  questionIds: ["q1", "q2"],
  reason?: string,
  forceRemoval?: boolean
}

// After
DELETE /questions/bulk-remove
Body: { 
  worksheetId: "uuid",
  questionIds: ["q1", "q2"],
  reason?: string,
  forceRemoval?: boolean
}
```

### Bulk Update Questions
```typescript
// Before
PATCH /worksheets/{worksheetId}/questions/bulk-update
Body: { 
  updates: [
    { questionId: "q1", updates: {...} },
    { questionId: "q2", updates: {...} }
  ],
  reason?: string,
  validateQuestions?: boolean
}

// After
PATCH /questions/bulk-update
Body: { 
  worksheetId: "uuid",
  updates: [
    { questionId: "q1", updates: {...} },
    { questionId: "q2", updates: {...} }
  ],
  reason?: string,
  validateQuestions?: boolean
}
```

## Migration Steps for Frontend

### 1. **Update API Base URLs**
Change all question-related API calls from `/worksheets/:id/questions/*` to `/questions/*`

### 2. **Add worksheetId to Request Bodies**
For all question operations, include `worksheetId` in the request body:

```typescript
// Example: Update question
const updateQuestion = async (worksheetId: string, questionId: string, updates: any) => {
  return await api.patch(`/questions/${questionId}`, {
    worksheetId,  // Add this
    ...updates
  });
};
```

### 3. **Update Reorder Questions Call**
The reorder endpoint should now work without validation errors:

```typescript
const reorderQuestions = async (worksheetId: string, reorders: ReorderOperation[]) => {
  return await api.patch('/questions/reorder', {
    worksheetId,  // Add this
    reorders
  });
};
```

### 4. **Test All Question Operations**
Verify that all question operations work with the new structure.

## Benefits

1. **Fixed Validation Issues**: The "property reorders should not exist" error is resolved
2. **Cleaner API Structure**: Simpler, more RESTful endpoints
3. **Better Separation of Concerns**: Questions have their own controller
4. **Easier Testing**: Simpler request structure
5. **Consistent Data Flow**: All data in request body, no mixed URL params + body

## Backward Compatibility

The old worksheet question endpoints are still available for now, but should be migrated to the new structure. The new Question Controller provides the same functionality with better validation and cleaner structure.

## Support

If you encounter any issues during migration, the new endpoints provide better error messages and debugging information to help identify problems quickly.
